================================================================================
                    GUIA COMPLETO DE JAVA PARA ENTREVISTAS - PARTE 2
================================================================================

                                  PÁGINA 9
                        ARQUITETURA E DESIGN - PARTE 1
================================================================================

1. DESIGN PATTERNS

SINGLETON:
- Garante uma única instância de uma classe
- Acesso global a essa instância

// Implementação thread-safe (Enum - recomendada)
public enum DatabaseConnection {
    INSTANCE;
    
    private Connection connection;
    
    private DatabaseConnection() {
        // inicializar conexão
    }
    
    public Connection getConnection() {
        return connection;
    }
}

// Uso
DatabaseConnection.INSTANCE.getConnection();

// Implementação clássica thread-safe
public class Singleton {
    private static volatile Singleton instance;
    
    private Singleton() {}
    
    public static Singleton getInstance() {
        if (instance == null) {
            synchronized (Singleton.class) {
                if (instance == null) {
                    instance = new Singleton();
                }
            }
        }
        return instance;
    }
}

FACTORY:
- Cria objetos sem especificar suas classes concretas

public abstract class Veiculo {
    public abstract void acelerar();
}

public class Carro extends Veiculo {
    @Override
    public void acelerar() {
        System.out.println("Carro acelerando");
    }
}

public class Moto extends Veiculo {
    @Override
    public void acelerar() {
        System.out.println("Moto acelerando");
    }
}

public class VeiculoFactory {
    public static Veiculo criarVeiculo(String tipo) {
        switch (tipo.toLowerCase()) {
            case "carro":
                return new Carro();
            case "moto":
                return new Moto();
            default:
                throw new IllegalArgumentException("Tipo de veículo inválido");
        }
    }
}

// Uso
Veiculo veiculo = VeiculoFactory.criarVeiculo("carro");
veiculo.acelerar();

STRATEGY:
- Define família de algoritmos e os torna intercambiáveis

public interface CalculadoraDesconto {
    double calcular(double valor);
}

public class DescontoNormal implements CalculadoraDesconto {
    @Override
    public double calcular(double valor) {
        return valor * 0.05; // 5%
    }
}

public class DescontoVIP implements CalculadoraDesconto {
    @Override
    public double calcular(double valor) {
        return valor * 0.15; // 15%
    }
}

public class CarrinhoCompras {
    private CalculadoraDesconto calculadoraDesconto;
    
    public CarrinhoCompras(CalculadoraDesconto calculadoraDesconto) {
        this.calculadoraDesconto = calculadoraDesconto;
    }
    
    public double calcularTotal(double valor) {
        double desconto = calculadoraDesconto.calcular(valor);
        return valor - desconto;
    }
    
    public void setCalculadoraDesconto(CalculadoraDesconto calculadoraDesconto) {
        this.calculadoraDesconto = calculadoraDesconto;
    }
}

// Uso
CarrinhoCompras carrinho = new CarrinhoCompras(new DescontoNormal());
double total = carrinho.calcularTotal(100.0); // 95.0

carrinho.setCalculadoraDesconto(new DescontoVIP());
total = carrinho.calcularTotal(100.0); // 85.0

OBSERVER:
- Define dependência um-para-muitos entre objetos

public interface Observer {
    void update(String evento);
}

public class Subject {
    private List<Observer> observers = new ArrayList<>();
    
    public void addObserver(Observer observer) {
        observers.add(observer);
    }
    
    public void removeObserver(Observer observer) {
        observers.remove(observer);
    }
    
    public void notifyObservers(String evento) {
        for (Observer observer : observers) {
            observer.update(evento);
        }
    }
}

public class EmailNotifier implements Observer {
    @Override
    public void update(String evento) {
        System.out.println("Email enviado para evento: " + evento);
    }
}

public class SMSNotifier implements Observer {
    @Override
    public void update(String evento) {
        System.out.println("SMS enviado para evento: " + evento);
    }
}

// Uso
Subject pedidoService = new Subject();
pedidoService.addObserver(new EmailNotifier());
pedidoService.addObserver(new SMSNotifier());

pedidoService.notifyObservers("Pedido criado"); // Notifica todos os observers

BUILDER:
- Constrói objetos complexos passo a passo

public class Produto {
    private String nome;
    private double preco;
    private String categoria;
    private String descricao;
    private boolean ativo;
    
    private Produto(Builder builder) {
        this.nome = builder.nome;
        this.preco = builder.preco;
        this.categoria = builder.categoria;
        this.descricao = builder.descricao;
        this.ativo = builder.ativo;
    }
    
    public static class Builder {
        private String nome;
        private double preco;
        private String categoria;
        private String descricao;
        private boolean ativo = true;
        
        public Builder nome(String nome) {
            this.nome = nome;
            return this;
        }
        
        public Builder preco(double preco) {
            this.preco = preco;
            return this;
        }
        
        public Builder categoria(String categoria) {
            this.categoria = categoria;
            return this;
        }
        
        public Builder descricao(String descricao) {
            this.descricao = descricao;
            return this;
        }
        
        public Builder ativo(boolean ativo) {
            this.ativo = ativo;
            return this;
        }
        
        public Produto build() {
            return new Produto(this);
        }
    }
    
    // getters...
}

// Uso
Produto produto = new Produto.Builder()
    .nome("Notebook")
    .preco(2500.0)
    .categoria("Eletrônicos")
    .descricao("Notebook para programação")
    .build();

================================================================================
                                  PÁGINA 10
                        ARQUITETURA E DESIGN - PARTE 2
================================================================================

2. ARQUITETURA EM CAMADAS

CONTROLLER (Camada de Apresentação):
- Recebe requisições
- Valida entrada
- Chama serviços
- Retorna resposta

@RestController
@RequestMapping("/api/usuarios")
public class UsuarioController {
    
    private final UsuarioService usuarioService;
    
    public UsuarioController(UsuarioService usuarioService) {
        this.usuarioService = usuarioService;
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<UsuarioDTO> buscarPorId(@PathVariable Long id) {
        try {
            UsuarioDTO usuario = usuarioService.buscarPorId(id);
            return ResponseEntity.ok(usuario);
        } catch (UsuarioNaoEncontradoException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    @PostMapping
    public ResponseEntity<UsuarioDTO> criar(@Valid @RequestBody CriarUsuarioDTO dto) {
        UsuarioDTO usuario = usuarioService.criar(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(usuario);
    }
}

SERVICE (Camada de Negócio):
- Lógica de negócio
- Coordena operações
- Transações

@Service
@Transactional
public class UsuarioService {
    
    private final UsuarioRepository usuarioRepository;
    private final EmailService emailService;
    private final UsuarioMapper usuarioMapper;
    
    public UsuarioService(UsuarioRepository usuarioRepository, 
                         EmailService emailService,
                         UsuarioMapper usuarioMapper) {
        this.usuarioRepository = usuarioRepository;
        this.emailService = emailService;
        this.usuarioMapper = usuarioMapper;
    }
    
    public UsuarioDTO buscarPorId(Long id) {
        Usuario usuario = usuarioRepository.findById(id)
            .orElseThrow(() -> new UsuarioNaoEncontradoException("Usuário não encontrado"));
        
        return usuarioMapper.toDTO(usuario);
    }
    
    public UsuarioDTO criar(CriarUsuarioDTO dto) {
        // Validações de negócio
        if (usuarioRepository.existsByEmail(dto.getEmail())) {
            throw new EmailJaExisteException("Email já cadastrado");
        }
        
        // Criar entidade
        Usuario usuario = usuarioMapper.toEntity(dto);
        usuario.setDataCriacao(LocalDateTime.now());
        usuario.setAtivo(true);
        
        // Salvar
        usuario = usuarioRepository.save(usuario);
        
        // Enviar email de boas-vindas
        emailService.enviarBoasVindas(usuario.getEmail(), usuario.getNome());
        
        return usuarioMapper.toDTO(usuario);
    }
}

REPOSITORY (Camada de Dados):
- Acesso a dados
- Abstrai persistência
- Queries

@Repository
public interface UsuarioRepository extends JpaRepository<Usuario, Long> {
    
    Optional<Usuario> findByEmail(String email);
    
    boolean existsByEmail(String email);
    
    List<Usuario> findByAtivoTrue();
    
    @Query("SELECT u FROM Usuario u WHERE u.nome LIKE %:nome% AND u.ativo = true")
    List<Usuario> buscarPorNomeAtivo(@Param("nome") String nome);
    
    @Modifying
    @Query("UPDATE Usuario u SET u.ultimoAcesso = :data WHERE u.id = :id")
    void atualizarUltimoAcesso(@Param("id") Long id, @Param("data") LocalDateTime data);
}

3. DTOs E MAPEAMENTO

DTO (Data Transfer Object):
- Transferir dados entre camadas
- Evitar exposição de entidades
- Validações específicas

public class UsuarioDTO {
    private Long id;
    private String nome;
    private String email;
    private LocalDateTime dataCriacao;
    private boolean ativo;
    
    // construtores, getters, setters
}

public class CriarUsuarioDTO {
    @NotBlank(message = "Nome é obrigatório")
    @Size(min = 2, max = 100, message = "Nome deve ter entre 2 e 100 caracteres")
    private String nome;
    
    @NotBlank(message = "Email é obrigatório")
    @Email(message = "Email deve ser válido")
    private String email;
    
    @NotBlank(message = "Senha é obrigatória")
    @Size(min = 8, message = "Senha deve ter pelo menos 8 caracteres")
    private String senha;
    
    // getters, setters
}

MAPPER:
- Converte entre entidades e DTOs
- Centraliza lógica de conversão

@Component
public class UsuarioMapper {
    
    public UsuarioDTO toDTO(Usuario usuario) {
        if (usuario == null) return null;
        
        UsuarioDTO dto = new UsuarioDTO();
        dto.setId(usuario.getId());
        dto.setNome(usuario.getNome());
        dto.setEmail(usuario.getEmail());
        dto.setDataCriacao(usuario.getDataCriacao());
        dto.setAtivo(usuario.isAtivo());
        
        return dto;
    }
    
    public Usuario toEntity(CriarUsuarioDTO dto) {
        if (dto == null) return null;
        
        Usuario usuario = new Usuario();
        usuario.setNome(dto.getNome());
        usuario.setEmail(dto.getEmail());
        usuario.setSenha(passwordEncoder.encode(dto.getSenha()));
        
        return usuario;
    }
    
    public List<UsuarioDTO> toDTOList(List<Usuario> usuarios) {
        return usuarios.stream()
            .map(this::toDTO)
            .collect(Collectors.toList());
    }
}

VANTAGENS DA ARQUITETURA EM CAMADAS:
- Separação de responsabilidades
- Facilita testes
- Manutenibilidade
- Reutilização
- Flexibilidade para mudanças

EXEMPLO DE FLUXO:
1. Controller recebe requisição HTTP
2. Controller valida dados de entrada
3. Controller chama Service
4. Service executa lógica de negócio
5. Service chama Repository
6. Repository acessa banco de dados
7. Repository retorna entidade
8. Service converte para DTO
9. Controller retorna resposta HTTP

================================================================================
                                  PÁGINA 11
                           JAVA AVANÇADO - PARTE 1
================================================================================

1. GENERICS

CONCEITO:
- Permite criar classes, interfaces e métodos que trabalham com tipos parametrizados
- Type safety em tempo de compilação
- Elimina necessidade de casting
- Introduzido no Java 5

SINTAXE BÁSICA:
// Classe genérica
public class Caixa<T> {
    private T conteudo;

    public void colocar(T item) {
        this.conteudo = item;
    }

    public T retirar() {
        return conteudo;
    }
}

// Uso
Caixa<String> caixaTexto = new Caixa<>();
caixaTexto.colocar("Hello World");
String texto = caixaTexto.retirar(); // Sem casting necessário

MÉTODOS GENÉRICOS:
public class Utilitarios {
    // Método genérico estático
    public static <T> void trocar(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    // Método genérico com retorno
    public static <T> T obterPrimeiro(List<T> lista) {
        return lista.isEmpty() ? null : lista.get(0);
    }
}

WILDCARDS:
// ? extends T (upper bound) - pode ler, não pode escrever
public void processarNumeros(List<? extends Number> numeros) {
    for (Number num : numeros) {
        System.out.println(num.doubleValue());
    }
}

// ? super T (lower bound) - pode escrever, leitura limitada
public void adicionarNumeros(List<? super Integer> lista) {
    lista.add(10);
    lista.add(20);
}

2. LAMBDA EXPRESSIONS (Java 8+)

CONCEITO:
- Função anônima concisa
- Implementação de interfaces funcionais
- Sintaxe: (parâmetros) -> expressão

SINTAXE:
// Sem parâmetros
() -> System.out.println("Hello")

// Um parâmetro
x -> x * 2

// Múltiplos parâmetros
(x, y) -> x + y

// Corpo com múltiplas linhas
(x, y) -> {
    int resultado = x + y;
    return resultado;
}

INTERFACES FUNCIONAIS PREDEFINIDAS:
// Predicate<T> - testa condição
Predicate<String> naoVazio = s -> !s.isEmpty();

// Function<T, R> - transforma T em R
Function<String, Integer> tamanho = s -> s.length();

// Consumer<T> - consome T sem retorno
Consumer<String> imprimir = s -> System.out.println(s);

// Supplier<T> - fornece T
Supplier<String> fornecedor = () -> "Hello World";

USO COM COLEÇÕES:
List<String> nomes = Arrays.asList("João", "Maria", "Pedro", "Ana");

// Filtrar
List<String> nomesComJ = nomes.stream()
    .filter(nome -> nome.startsWith("J"))
    .collect(Collectors.toList());

// Transformar
List<Integer> tamanhos = nomes.stream()
    .map(String::length)
    .collect(Collectors.toList());

// ForEach
nomes.forEach(System.out::println);

3. STREAMS API (Java 8+)

CONCEITO:
- Processamento funcional de coleções
- Operações intermediárias (lazy) e terminais
- Não modifica coleção original

CRIAÇÃO DE STREAMS:
// De coleção
Stream<String> stream1 = lista.stream();

// De array
Stream<String> stream2 = Arrays.stream(array);

// Valores diretos
Stream<String> stream3 = Stream.of("a", "b", "c");

OPERAÇÕES INTERMEDIÁRIAS:
List<String> palavras = Arrays.asList("java", "python", "javascript", "go");

// filter - filtra elementos
List<String> palavrasLongas = palavras.stream()
    .filter(p -> p.length() > 4)
    .collect(Collectors.toList());

// map - transforma elementos
List<Integer> tamanhos = palavras.stream()
    .map(String::length)
    .collect(Collectors.toList());

// sorted - ordena
List<String> ordenadas = palavras.stream()
    .sorted()
    .collect(Collectors.toList());

OPERAÇÕES TERMINAIS:
List<Integer> numeros = Arrays.asList(1, 2, 3, 4, 5);

// collect - coleta em coleção
List<Integer> pares = numeros.stream()
    .filter(n -> n % 2 == 0)
    .collect(Collectors.toList());

// reduce - reduz a um valor
int soma = numeros.stream()
    .reduce(0, (a, b) -> a + b);

// count - conta elementos
long quantidade = numeros.stream()
    .filter(n -> n > 3)
    .count();

// anyMatch, allMatch, noneMatch
boolean temPar = numeros.stream().anyMatch(n -> n % 2 == 0);

================================================================================
