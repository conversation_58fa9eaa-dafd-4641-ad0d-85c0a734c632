================================================================================
                    GUIA COMPLETO DE JAVA PARA ENTREVISTAS - PARTE 4
================================================================================

                                  PÁGINA 16
                                   TESTES
================================================================================

1. JUNIT 5

CONFIGURAÇÃO:
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.9.2</version>
    <scope>test</scope>
</dependency>

ANOTAÇÕES PRINCIPAIS:
@Test - marca método de teste
@BeforeEach - executa antes de cada teste
@AfterEach - executa após cada teste
@BeforeAll - executa uma vez antes de todos os testes
@AfterAll - executa uma vez após todos os testes
@DisplayName - nome personalizado para o teste
@Disabled - desabilita teste
@ParameterizedTest - teste parametrizado

EXEMPLO BÁSICO:
public class CalculadoraTest {
    
    private Calculadora calculadora;
    
    @BeforeEach
    void setUp() {
        calculadora = new Calculadora();
    }
    
    @Test
    @DisplayName("Deve somar dois números positivos")
    void deveSomarDoisNumerosPositivos() {
        // Arrange (Preparar)
        int a = 5;
        int b = 3;
        
        // Act (Executar)
        int resultado = calculadora.somar(a, b);
        
        // Assert (Verificar)
        assertEquals(8, resultado);
    }
    
    @Test
    void deveRetornarZeroQuandoSomarZeros() {
        assertEquals(0, calculadora.somar(0, 0));
    }
    
    @Test
    void deveLancarExcecaoQuandoDividirPorZero() {
        assertThrows(ArithmeticException.class, () -> {
            calculadora.dividir(10, 0);
        });
    }
}

ASSERTIONS COMUNS:
// Igualdade
assertEquals(esperado, atual);
assertNotEquals(naoEsperado, atual);

// Verdadeiro/Falso
assertTrue(condicao);
assertFalse(condicao);

// Nulo
assertNull(objeto);
assertNotNull(objeto);

// Exceções
assertThrows(TipoExcecao.class, () -> codigo);
assertDoesNotThrow(() -> codigo);

// Coleções
assertIterableEquals(listaEsperada, listaAtual);

// Múltiplas assertions
assertAll(
    () -> assertEquals("João", pessoa.getNome()),
    () -> assertEquals(30, pessoa.getIdade()),
    () -> assertTrue(pessoa.isAtivo())
);

TESTES PARAMETRIZADOS:
@ParameterizedTest
@ValueSource(ints = {1, 2, 3, 5, 8, 13})
void deveRetornarTrueParaNumerosPrimos(int numero) {
    assertTrue(MathUtils.isPrimo(numero));
}

@ParameterizedTest
@CsvSource({
    "1, 1, 2",
    "2, 3, 5",
    "5, 7, 12"
})
void deveSomarCorretamente(int a, int b, int esperado) {
    assertEquals(esperado, calculadora.somar(a, b));
}

2. MOCKITO

CONFIGURAÇÃO:
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <version>5.1.1</version>
    <scope>test</scope>
</dependency>

CRIANDO MOCKS:
// Anotação
@Mock
private UsuarioRepository usuarioRepository;

@InjectMocks
private UsuarioService usuarioService;

// Programaticamente
UsuarioRepository mock = Mockito.mock(UsuarioRepository.class);

CONFIGURANDO COMPORTAMENTO:
// Retorno simples
when(usuarioRepository.findById(1L)).thenReturn(Optional.of(usuario));

// Múltiplos retornos
when(usuarioRepository.findAll())
    .thenReturn(Arrays.asList(usuario1))
    .thenReturn(Arrays.asList(usuario1, usuario2));

// Exceção
when(usuarioRepository.findById(999L))
    .thenThrow(new UsuarioNaoEncontradoException("Usuário não encontrado"));

// Qualquer argumento
when(usuarioRepository.findById(any(Long.class))).thenReturn(Optional.of(usuario));

// Argumentos específicos
when(usuarioRepository.findByEmail(eq("<EMAIL>"))).thenReturn(Optional.of(usuario));

VERIFICAÇÕES:
// Verificar se método foi chamado
verify(usuarioRepository).findById(1L);

// Verificar número de chamadas
verify(usuarioRepository, times(2)).findAll();
verify(usuarioRepository, never()).delete(any());

// Verificar ordem de chamadas
InOrder inOrder = inOrder(usuarioRepository, emailService);
inOrder.verify(usuarioRepository).save(any());
inOrder.verify(emailService).enviar(any());

EXEMPLO COMPLETO:
@ExtendWith(MockitoExtension.class)
class UsuarioServiceTest {
    
    @Mock
    private UsuarioRepository usuarioRepository;
    
    @Mock
    private EmailService emailService;
    
    @InjectMocks
    private UsuarioService usuarioService;
    
    @Test
    void deveCriarUsuarioComSucesso() {
        // Arrange
        CriarUsuarioDTO dto = new CriarUsuarioDTO("João", "<EMAIL>");
        Usuario usuario = new Usuario("João", "<EMAIL>");
        
        when(usuarioRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(usuarioRepository.save(any(Usuario.class))).thenReturn(usuario);
        
        // Act
        UsuarioDTO resultado = usuarioService.criar(dto);
        
        // Assert
        assertNotNull(resultado);
        assertEquals("João", resultado.getNome());
        assertEquals("<EMAIL>", resultado.getEmail());
        
        verify(usuarioRepository).existsByEmail("<EMAIL>");
        verify(usuarioRepository).save(any(Usuario.class));
        verify(emailService).enviarBoasVindas("<EMAIL>", "João");
    }
    
    @Test
    void deveLancarExcecaoQuandoEmailJaExistir() {
        // Arrange
        CriarUsuarioDTO dto = new CriarUsuarioDTO("João", "<EMAIL>");
        when(usuarioRepository.existsByEmail("<EMAIL>")).thenReturn(true);
        
        // Act & Assert
        assertThrows(EmailJaExisteException.class, () -> {
            usuarioService.criar(dto);
        });
        
        verify(usuarioRepository).existsByEmail("<EMAIL>");
        verify(usuarioRepository, never()).save(any());
        verify(emailService, never()).enviarBoasVindas(any(), any());
    }
}

3. TESTES DE INTEGRAÇÃO

CONCEITO:
- Testam interação entre componentes
- Usam contexto real da aplicação
- Testam fluxo completo

SPRING BOOT TEST:
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
class UsuarioIntegrationTest {
    
    @Autowired
    private UsuarioService usuarioService;
    
    @Autowired
    private UsuarioRepository usuarioRepository;
    
    @Test
    void deveIntegrarCorretamenteComBancoDeDados() {
        // Arrange
        CriarUsuarioDTO dto = new CriarUsuarioDTO("João", "<EMAIL>");
        
        // Act
        UsuarioDTO resultado = usuarioService.criar(dto);
        
        // Assert
        assertNotNull(resultado.getId());
        
        Optional<Usuario> usuarioSalvo = usuarioRepository.findById(resultado.getId());
        assertTrue(usuarioSalvo.isPresent());
        assertEquals("João", usuarioSalvo.get().getNome());
    }
}

TESTE DE CONTROLLER:
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class UsuarioControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void deveCriarUsuarioViaAPI() {
        // Arrange
        CriarUsuarioDTO dto = new CriarUsuarioDTO("João", "<EMAIL>");
        
        // Act
        ResponseEntity<UsuarioDTO> response = restTemplate.postForEntity(
            "/api/usuarios", dto, UsuarioDTO.class);
        
        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("João", response.getBody().getNome());
    }
}

TESTCONTAINERS (Banco real para testes):
@SpringBootTest
@Testcontainers
class UsuarioTestContainersTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
    }
    
    @Autowired
    private UsuarioRepository usuarioRepository;
    
    @Test
    void deveTestarComBancoReal() {
        Usuario usuario = new Usuario("João", "<EMAIL>");
        Usuario salvo = usuarioRepository.save(usuario);
        
        assertNotNull(salvo.getId());
        assertTrue(usuarioRepository.existsByEmail("<EMAIL>"));
    }
}

4. BOAS PRÁTICAS DE TESTE

ESTRUTURA AAA:
// Arrange - preparar dados e mocks
// Act - executar código sendo testado
// Assert - verificar resultados

NOMES DESCRITIVOS:
// ❌ Ruim
@Test
void test1() { }

// ✅ Bom
@Test
void deveCriarUsuarioQuandoDadosValidos() { }

UM CONCEITO POR TESTE:
// ❌ Teste fazendo muitas coisas
@Test
void testeCompleto() {
    // testa criação
    // testa busca
    // testa atualização
    // testa exclusão
}

// ✅ Testes específicos
@Test
void deveCriarUsuario() { }

@Test
void deveBuscarUsuario() { }

DADOS DE TESTE:
// Builder pattern para dados de teste
public class UsuarioTestDataBuilder {
    private String nome = "João";
    private String email = "<EMAIL>";
    private boolean ativo = true;
    
    public UsuarioTestDataBuilder comNome(String nome) {
        this.nome = nome;
        return this;
    }
    
    public UsuarioTestDataBuilder comEmail(String email) {
        this.email = email;
        return this;
    }
    
    public UsuarioTestDataBuilder inativo() {
        this.ativo = false;
        return this;
    }
    
    public Usuario build() {
        return new Usuario(nome, email, ativo);
    }
}

// Uso
Usuario usuario = new UsuarioTestDataBuilder()
    .comNome("Maria")
    .comEmail("<EMAIL>")
    .build();

================================================================================
                                  PÁGINA 17
                        FERRAMENTAS E ECOSSISTEMA
================================================================================

1. MAVEN / GRADLE

MAVEN - Ferramenta de build baseada em XML:

POM.XML BÁSICO:
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.empresa</groupId>
    <artifactId>meu-projeto</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.1.0</version>
        </dependency>
    </dependencies>
</project>

COMANDOS MAVEN:
mvn clean          # limpa target/
mvn compile        # compila código
mvn test           # executa testes
mvn package        # gera JAR/WAR
mvn install        # instala no repositório local

GRADLE - Ferramenta de build baseada em Groovy/Kotlin:

BUILD.GRADLE:
plugins {
    id 'java'
    id 'org.springframework.boot' version '3.1.0'
}

group = 'com.empresa'
version = '1.0.0'
sourceCompatibility = '17'

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

COMANDOS GRADLE:
./gradlew clean        # limpa build/
./gradlew build        # compila e testa
./gradlew test         # executa testes

2. GIT E GITFLOW

COMANDOS BÁSICOS GIT:
git init                    # inicializa repositório
git clone <url>            # clona repositório
git add .                  # adiciona arquivos ao stage
git commit -m "mensagem"   # confirma mudanças
git push origin main       # envia para repositório remoto
git pull origin main       # puxa mudanças do remoto

BRANCHES:
git branch                 # lista branches
git checkout -b feature/nova # cria e muda para branch
git merge feature/nova     # faz merge da branch
git branch -d feature/nova # deleta branch

GITFLOW - Modelo de branching:
- main/master: código de produção
- develop: código de desenvolvimento
- feature/*: novas funcionalidades
- release/*: preparação para release
- hotfix/*: correções urgentes

3. DOCKER

DOCKERFILE:
FROM openjdk:17-jdk-slim
WORKDIR /app
COPY target/minha-app.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]

COMANDOS DOCKER:
docker build -t minha-app .        # constrói imagem
docker run -p 8080:8080 minha-app  # executa container
docker ps                          # lista containers rodando

DOCKER-COMPOSE:
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - db
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: mydb
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password

4. CI/CD - GITHUB ACTIONS

.github/workflows/ci.yml:
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    - name: Run tests
      run: mvn clean test

5. MONITORAMENTO - SPRING BOOT ACTUATOR

CONFIGURAÇÃO:
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>

ENDPOINTS ÚTEIS:
/actuator/health     # status da aplicação
/actuator/info       # informações da aplicação
/actuator/metrics    # métricas da aplicação

MÉTRICAS CUSTOMIZADAS:
@Component
public class CustomMetrics {
    private final Counter pedidosCounter;

    public CustomMetrics(MeterRegistry meterRegistry) {
        this.pedidosCounter = Counter.builder("pedidos.total")
            .description("Total de pedidos processados")
            .register(meterRegistry);
    }

    public void incrementarPedidos() {
        pedidosCounter.increment();
    }
}

LOGGING:
logging:
  level:
    com.empresa: DEBUG
    org.springframework: INFO
  file:
    name: logs/application.log

================================================================================
                                 CONCLUSÃO
================================================================================

Este guia abrange os principais conceitos de Java necessários para entrevistas
técnicas, desde fundamentos básicos até tópicos avançados.

DICAS PARA ENTREVISTAS:
1. Pratique codificação em tempo real
2. Explique seu raciocínio enquanto programa
3. Considere casos extremos e tratamento de erros
4. Demonstre conhecimento de boas práticas
5. Esteja preparado para discussões sobre arquitetura
6. Conheça as ferramentas do ecossistema Java
7. Pratique algoritmos e estruturas de dados
8. Entenda conceitos de programação orientada a objetos
9. Saiba explicar trade-offs de diferentes abordagens
10. Mantenha-se atualizado com as versões mais recentes do Java

PRÓXIMOS PASSOS:
- Pratique implementando projetos reais
- Contribua para projetos open source
- Estude padrões de arquitetura (microserviços, event-driven)
- Aprofunde conhecimentos em Spring Framework
- Explore tecnologias complementares (Kafka, Redis, Elasticsearch)

Boa sorte em sua preparação e nas entrevistas!

================================================================================
