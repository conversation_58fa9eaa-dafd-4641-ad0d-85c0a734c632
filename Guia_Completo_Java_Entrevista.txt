================================================================================
                    GUIA COMPLETO DE JAVA PARA ENTREVISTAS
================================================================================

ÍNDICE:
Página 1: Fundamentos de Java - Parte 1
Página 2: Fundamentos de Java - Parte 2  
Página 3: Programação Orientada a Objetos - Parte 1
Página 4: Programação Orientada a Objetos - Parte 2
Página 5: Coleções e Estruturas de Dados - Parte 1
Página 6: Coleções e Estruturas de Dados - Parte 2
Página 7: Tratamento de Erros
Página 8: Boas <PERSON>rá<PERSON>s
Página 9: Arquitetura e Design - Parte 1
Página 10: Arquitetura e Design - Parte 2
Página 11: Java Avançado - Parte 1
Página 12: Java Avançado - Parte 2
Página 13: Integração e Comunicação - Parte 1
Página 14: Integração e Comunicação - Parte 2
Página 15: Concorrência e Multithreading
Página 16: Testes
Página 17: Ferramentas e Ecossistema

================================================================================
                                  PÁGINA 1
                        FUNDAMENTOS DE JAVA - PARTE 1
================================================================================

1. DIFERENÇAS ENTRE VERSÕES DO JAVA (6 até atual)

JAVA 7 (2011):
- Try-with-resources (AutoCloseable)
- Diamond operator (<>)
- Switch com Strings
- Exemplo: try (FileReader fr = new FileReader("file.txt")) { ... }

JAVA 8 (2014) - MARCO IMPORTANTE:
- Lambda Expressions: (x, y) -> x + y
- Stream API: list.stream().filter(x -> x > 5).collect(toList())
- Optional: Optional.ofNullable(value)
- Interface com métodos default

JAVA 9 (2017):
- Módulos (Project Jigsaw)
- JShell (REPL)
- Factory methods para Collections: List.of(), Set.of()

JAVA 11 (2018) - LTS:
- var para variáveis locais
- Novos métodos String: isBlank(), lines(), strip()
- HttpClient nativo

JAVA 17 (2021) - LTS ATUAL:
- Records: public record Person(String name, int age) {}
- Pattern Matching para instanceof
- Sealed Classes

JAVA 21 (2023) - LTS MAIS RECENTE:
- Virtual Threads (Project Loom)
- Pattern Matching para switch
- String Templates (Preview)

2. SINTAXE BÁSICA

VARIÁVEIS:
- Primitivas: int, double, boolean, char, byte, short, long, float
- Referência: String, Arrays, Objetos

OPERADORES:
- Aritméticos: +, -, *, /, %
- Relacionais: ==, !=, <, >, <=, >=
- Lógicos: &&, ||, !
- Atribuição: =, +=, -=, *=, /=

CONTROLE DE FLUXO:
if (condição) { ... } else { ... }
for (int i = 0; i < 10; i++) { ... }
while (condição) { ... }
do { ... } while (condição);
switch (variável) { case valor: break; default: }

3. TIPOS DE DADOS PRIMITIVOS

INTEIROS:
- byte: -128 a 127 (8 bits)
- short: -32.768 a 32.767 (16 bits)
- int: -2^31 a 2^31-1 (32 bits) - PADRÃO
- long: -2^63 a 2^63-1 (64 bits) - sufixo L

PONTO FLUTUANTE:
- float: 32 bits - sufixo f (3.14f)
- double: 64 bits - PADRÃO (3.14)

OUTROS:
- boolean: true/false
- char: caractere Unicode (16 bits) - 'A'

DIFERENÇAS IMPORTANTES:
- Primitivos são armazenados na stack
- Objetos são armazenados na heap
- Primitivos têm wrappers: Integer, Double, Boolean, etc.
- Autoboxing/Unboxing automático desde Java 5

================================================================================
                                  PÁGINA 2
                        FUNDAMENTOS DE JAVA - PARTE 2
================================================================================

4. CLASSES E OBJETOS

DEFINIÇÃO DE CLASSE:
public class Pessoa {
    // Atributos (estado)
    private String nome;
    private int idade;
    
    // Construtor
    public Pessoa(String nome, int idade) {
        this.nome = nome;
        this.idade = idade;
    }
    
    // Métodos (comportamento)
    public void falar() {
        System.out.println(nome + " está falando");
    }
}

CRIAÇÃO DE OBJETOS:
Pessoa pessoa = new Pessoa("João", 25);
pessoa.falar();

CONCEITOS IMPORTANTES:
- Classe é o molde, objeto é a instância
- new aloca memória na heap
- Referência aponta para o objeto
- Garbage Collector limpa objetos não referenciados

5. MÉTODOS E CONSTRUTORES

MÉTODOS:
[modificador] [static] tipoRetorno nomeMetodo(parâmetros) {
    // corpo do método
    return valor; // se não for void
}

SOBRECARGA (Overloading):
public void calcular(int a, int b) { ... }
public void calcular(double a, double b) { ... }
public void calcular(int a, int b, int c) { ... }

CONSTRUTORES:
- Mesmo nome da classe
- Não tem tipo de retorno
- Construtor padrão (sem parâmetros) é criado automaticamente
- this() chama outro construtor da mesma classe
- super() chama construtor da classe pai

EXEMPLO:
public class Conta {
    private double saldo;
    
    public Conta() {
        this(0.0); // chama construtor com parâmetro
    }
    
    public Conta(double saldoInicial) {
        this.saldo = saldoInicial;
    }
}

6. MODIFICADORES DE ACESSO

PUBLIC:
- Acessível de qualquer lugar
- Classes, métodos, atributos

PRIVATE:
- Acessível apenas dentro da própria classe
- Encapsulamento

PROTECTED:
- Acessível na mesma package e subclasses
- Herança

PACKAGE-PRIVATE (padrão):
- Acessível apenas na mesma package
- Sem modificador explícito

EXEMPLO PRÁTICO:
public class ContaBancaria {
    private double saldo;           // só a classe acessa
    protected String numeroConta;   // package + herança
    public String titular;          // acesso total
    
    public double getSaldo() {      // getter público
        return saldo;
    }
    
    private void validarSaldo() {   // método interno
        // validações
    }
}

7. PACOTES E IMPORT

PACOTES:
- Organizam classes relacionadas
- Evitam conflitos de nomes
- Estrutura de diretórios

DECLARAÇÃO:
package com.empresa.projeto.modelo;

IMPORT:
import java.util.List;           // classe específica
import java.util.*;              // todas as classes (evitar)
import static Math.PI;           // import estático

EXEMPLO DE ESTRUTURA:
src/
  com/
    empresa/
      projeto/
        modelo/
          Pessoa.java
        servico/
          PessoaService.java
        controlador/
          PessoaController.java

================================================================================
                                  PÁGINA 3
                   PROGRAMAÇÃO ORIENTADA A OBJETOS - PARTE 1
================================================================================

1. ENCAPSULAMENTO

CONCEITO:
- Ocultar detalhes internos da implementação
- Controlar acesso aos dados através de métodos
- Proteger integridade dos dados

IMPLEMENTAÇÃO:
public class ContaBancaria {
    private double saldo;  // atributo privado

    // Getter - acesso controlado para leitura
    public double getSaldo() {
        return saldo;
    }

    // Setter - acesso controlado para escrita
    public void setSaldo(double saldo) {
        if (saldo >= 0) {  // validação
            this.saldo = saldo;
        } else {
            throw new IllegalArgumentException("Saldo não pode ser negativo");
        }
    }

    // Método que encapsula lógica de negócio
    public void sacar(double valor) {
        if (valor > 0 && valor <= saldo) {
            saldo -= valor;
        } else {
            throw new IllegalArgumentException("Valor inválido para saque");
        }
    }
}

VANTAGENS:
- Segurança dos dados
- Facilita manutenção
- Permite validações
- Flexibilidade para mudanças internas

2. HERANÇA

CONCEITO:
- Reutilização de código
- Relação "é um" (is-a)
- Classe filha herda atributos e métodos da classe pai

IMPLEMENTAÇÃO:
// Classe pai (superclasse)
public class Veiculo {
    protected String marca;
    protected String modelo;

    public Veiculo(String marca, String modelo) {
        this.marca = marca;
        this.modelo = modelo;
    }

    public void acelerar() {
        System.out.println("Veículo acelerando...");
    }
}

// Classe filha (subclasse)
public class Carro extends Veiculo {
    private int numeroPortas;

    public Carro(String marca, String modelo, int numeroPortas) {
        super(marca, modelo);  // chama construtor da classe pai
        this.numeroPortas = numeroPortas;
    }

    @Override  // sobrescrita de método
    public void acelerar() {
        System.out.println("Carro acelerando com " + numeroPortas + " portas");
    }

    // Método específico da classe filha
    public void abrirPorta() {
        System.out.println("Abrindo porta do carro");
    }
}

PALAVRAS-CHAVE:
- extends: estabelece herança
- super: referencia classe pai
- @Override: indica sobrescrita de método
- final: impede herança (classe) ou sobrescrita (método)

3. POLIMORFISMO

CONCEITO:
- "Muitas formas"
- Mesmo método, comportamentos diferentes
- Permite tratar objetos de classes diferentes de forma uniforme

TIPOS:
a) Polimorfismo de Sobrecarga (Overloading):
public class Calculadora {
    public int somar(int a, int b) {
        return a + b;
    }

    public double somar(double a, double b) {
        return a + b;
    }

    public int somar(int a, int b, int c) {
        return a + b + c;
    }
}

b) Polimorfismo de Sobrescrita (Overriding):
public class Animal {
    public void emitirSom() {
        System.out.println("Animal fazendo som");
    }
}

public class Cachorro extends Animal {
    @Override
    public void emitirSom() {
        System.out.println("Au au!");
    }
}

public class Gato extends Animal {
    @Override
    public void emitirSom() {
        System.out.println("Miau!");
    }
}

// Uso polimórfico
Animal[] animais = {new Cachorro(), new Gato()};
for (Animal animal : animais) {
    animal.emitirSom();  // Chama método específico de cada classe
}

VANTAGENS:
- Flexibilidade
- Extensibilidade
- Manutenibilidade
- Reutilização de código

================================================================================
                                  PÁGINA 4
                   PROGRAMAÇÃO ORIENTADA A OBJETOS - PARTE 2
================================================================================

4. ABSTRAÇÃO

CONCEITO:
- Ocultar complexidade desnecessária
- Mostrar apenas funcionalidades essenciais
- Implementada através de classes abstratas e interfaces

CLASSES ABSTRATAS:
- Não podem ser instanciadas
- Podem ter métodos concretos e abstratos
- Herança simples (extends)

public abstract class Forma {
    protected String cor;

    public Forma(String cor) {
        this.cor = cor;
    }

    // Método concreto
    public String getCor() {
        return cor;
    }

    // Método abstrato - deve ser implementado pelas subclasses
    public abstract double calcularArea();
    public abstract void desenhar();
}

public class Circulo extends Forma {
    private double raio;

    public Circulo(String cor, double raio) {
        super(cor);
        this.raio = raio;
    }

    @Override
    public double calcularArea() {
        return Math.PI * raio * raio;
    }

    @Override
    public void desenhar() {
        System.out.println("Desenhando círculo " + cor);
    }
}

5. INTERFACES

CONCEITO:
- Contrato que define o que uma classe deve implementar
- Todos os métodos são implicitamente public e abstract (até Java 8)
- Herança múltipla (implements)
- Desde Java 8: métodos default e static

IMPLEMENTAÇÃO:
public interface Voador {
    // Constante (implicitamente public static final)
    int VELOCIDADE_MAXIMA = 1000;

    // Método abstrato (implicitamente public abstract)
    void voar();
    void pousar();

    // Método default (Java 8+)
    default void planar() {
        System.out.println("Planando...");
    }

    // Método estático (Java 8+)
    static void verificarCondicoesTempo() {
        System.out.println("Verificando condições do tempo");
    }
}

public interface Nadador {
    void nadar();
    void mergulhar();
}

// Implementação múltipla
public class Pato implements Voador, Nadador {
    @Override
    public void voar() {
        System.out.println("Pato voando");
    }

    @Override
    public void pousar() {
        System.out.println("Pato pousando");
    }

    @Override
    public void nadar() {
        System.out.println("Pato nadando");
    }

    @Override
    public void mergulhar() {
        System.out.println("Pato mergulhando");
    }
}

DIFERENÇAS CLASSE ABSTRATA vs INTERFACE:

CLASSE ABSTRATA:
- Herança simples (extends)
- Pode ter atributos de instância
- Pode ter construtores
- Métodos concretos e abstratos
- Modificadores de acesso variados

INTERFACE:
- Herança múltipla (implements)
- Apenas constantes (public static final)
- Não tem construtores
- Métodos abstratos, default e static
- Métodos implicitamente public

QUANDO USAR:
- Interface: quando você quer definir um contrato, capacidade ("pode fazer")
- Classe Abstrata: quando você quer compartilhar código comum ("é um tipo de")

EXEMPLO PRÁTICO:
// Interface para capacidade
interface Pagavel {
    void processarPagamento(double valor);
}

// Classe abstrata para hierarquia
abstract class Funcionario {
    protected String nome;
    protected double salario;

    public abstract double calcularSalario();
}

// Implementação
class FuncionarioCLT extends Funcionario implements Pagavel {
    @Override
    public double calcularSalario() {
        return salario;
    }

    @Override
    public void processarPagamento(double valor) {
        System.out.println("Pagamento de R$ " + valor + " processado");
    }
}

================================================================================
                                  PÁGINA 5
                    COLEÇÕES E ESTRUTURAS DE DADOS - PARTE 1
================================================================================

1. ARRAYS

CONCEITO:
- Estrutura de dados homogênea
- Tamanho fixo definido na criação
- Índices começam em 0
- Armazenados de forma contígua na memória

DECLARAÇÃO E INICIALIZAÇÃO:
// Declaração
int[] numeros;
String[] nomes;

// Inicialização
numeros = new int[5];           // array de 5 inteiros (valores padrão: 0)
nomes = new String[3];          // array de 3 strings (valores padrão: null)

// Declaração e inicialização juntas
int[] valores = {1, 2, 3, 4, 5};
String[] cores = new String[]{"azul", "verde", "vermelho"};

OPERAÇÕES:
int[] array = {10, 20, 30, 40, 50};

// Acesso
int primeiro = array[0];        // 10
int ultimo = array[array.length - 1];  // 50

// Modificação
array[2] = 35;                  // {10, 20, 35, 40, 50}

// Iteração
for (int i = 0; i < array.length; i++) {
    System.out.println(array[i]);
}

// Enhanced for (for-each)
for (int valor : array) {
    System.out.println(valor);
}

ARRAYS MULTIDIMENSIONAIS:
int[][] matriz = new int[3][4];     // 3 linhas, 4 colunas
int[][] tabela = {
    {1, 2, 3},
    {4, 5, 6},
    {7, 8, 9}
};

// Acesso
int elemento = tabela[1][2];        // 6

LIMITAÇÕES:
- Tamanho fixo
- Não tem métodos úteis (add, remove, etc.)
- Verificação manual de limites

2. ARRAYLIST

CONCEITO:
- Array dinâmico redimensionável
- Implementa interface List
- Permite duplicatas
- Mantém ordem de inserção
- Não é thread-safe

DECLARAÇÃO E USO:
import java.util.ArrayList;
import java.util.List;

// Declaração
List<String> lista = new ArrayList<>();
ArrayList<Integer> numeros = new ArrayList<>();

// Adição
lista.add("Java");
lista.add("Python");
lista.add("JavaScript");
lista.add(1, "C++");           // inserir na posição 1

// Acesso
String primeiro = lista.get(0);
int tamanho = lista.size();

// Modificação
lista.set(0, "Kotlin");        // substitui elemento na posição 0

// Remoção
lista.remove(0);               // remove por índice
lista.remove("Python");       // remove por objeto
lista.clear();                // remove todos

// Verificação
boolean existe = lista.contains("Java");
boolean vazia = lista.isEmpty();

ITERAÇÃO:
List<String> linguagens = Arrays.asList("Java", "Python", "C++");

// For tradicional
for (int i = 0; i < linguagens.size(); i++) {
    System.out.println(linguagens.get(i));
}

// Enhanced for
for (String linguagem : linguagens) {
    System.out.println(linguagem);
}

// Iterator
Iterator<String> it = linguagens.iterator();
while (it.hasNext()) {
    System.out.println(it.next());
}

// Stream (Java 8+)
linguagens.stream().forEach(System.out::println);

CARACTERÍSTICAS:
- Acesso aleatório O(1)
- Inserção/remoção no final O(1) amortizado
- Inserção/remoção no meio O(n)
- Busca O(n)

3. LINKEDLIST

CONCEITO:
- Lista duplamente ligada
- Implementa List e Deque
- Eficiente para inserções/remoções
- Não permite acesso aleatório eficiente

DECLARAÇÃO E USO:
import java.util.LinkedList;

LinkedList<String> lista = new LinkedList<>();

// Adição
lista.add("Primeiro");
lista.addFirst("Início");      // adiciona no início
lista.addLast("Fim");          // adiciona no final
lista.offer("Queue");          // adiciona como queue

// Acesso
String primeiro = lista.getFirst();
String ultimo = lista.getLast();
String elemento = lista.get(1);    // menos eficiente que ArrayList

// Remoção
String removido = lista.removeFirst();
String removidoFim = lista.removeLast();
String poll = lista.poll();         // remove e retorna primeiro (null se vazio)

QUANDO USAR:
- ArrayList: acesso frequente por índice, poucas inserções/remoções no meio
- LinkedList: muitas inserções/remoções, uso como queue/deque

COMPARAÇÃO DE PERFORMANCE:
Operação          | ArrayList | LinkedList
------------------|-----------|------------
get(index)        | O(1)      | O(n)
add(element)      | O(1)*     | O(1)
add(index, elem)  | O(n)      | O(n)
remove(index)     | O(n)      | O(n)
remove(element)   | O(n)      | O(n)

* O(1) amortizado, pode ser O(n) quando precisa redimensionar

================================================================================
                                  PÁGINA 6
                    COLEÇÕES E ESTRUTURAS DE DADOS - PARTE 2
================================================================================

1. HASHMAP

CONCEITO:
- Estrutura chave-valor (key-value)
- Baseado em hash table
- Não mantém ordem
- Permite uma chave null e múltiplos valores null
- Não é thread-safe

DECLARAÇÃO E USO:
import java.util.HashMap;
import java.util.Map;

Map<String, Integer> idades = new HashMap<>();

// Adição
idades.put("João", 25);
idades.put("Maria", 30);
idades.put("Pedro", 28);

// Acesso
Integer idade = idades.get("João");        // 25
Integer inexistente = idades.get("Ana");   // null

// Verificação
boolean temChave = idades.containsKey("Maria");     // true
boolean temValor = idades.containsValue(30);        // true

// Remoção
Integer removido = idades.remove("Pedro");          // 28
idades.clear();

// Métodos úteis (Java 8+)
idades.putIfAbsent("Carlos", 35);          // só adiciona se não existir
idades.getOrDefault("Ana", 0);             // retorna valor padrão se não existir
idades.computeIfAbsent("Luis", k -> 40);   // computa valor se ausente

ITERAÇÃO:
Map<String, Integer> map = Map.of("A", 1, "B", 2, "C", 3);

// Por chaves
for (String chave : map.keySet()) {
    System.out.println(chave + " = " + map.get(chave));
}

// Por valores
for (Integer valor : map.values()) {
    System.out.println(valor);
}

// Por entradas (mais eficiente)
for (Map.Entry<String, Integer> entry : map.entrySet()) {
    System.out.println(entry.getKey() + " = " + entry.getValue());
}

// Stream (Java 8+)
map.forEach((k, v) -> System.out.println(k + " = " + v));

2. HASHSET

CONCEITO:
- Coleção de elementos únicos
- Baseado em HashMap internamente
- Não mantém ordem
- Permite um elemento null
- Não é thread-safe

DECLARAÇÃO E USO:
import java.util.HashSet;
import java.util.Set;

Set<String> cores = new HashSet<>();

// Adição
cores.add("azul");
cores.add("verde");
cores.add("vermelho");
cores.add("azul");        // duplicata ignorada

// Verificação
boolean existe = cores.contains("azul");    // true
int tamanho = cores.size();                 // 3

// Remoção
boolean removido = cores.remove("verde");   // true

// Operações de conjunto
Set<String> outrasCore = Set.of("azul", "amarelo", "roxo");

// União
Set<String> uniao = new HashSet<>(cores);
uniao.addAll(outrasCore);

// Interseção
Set<String> intersecao = new HashSet<>(cores);
intersecao.retainAll(outrasCore);

// Diferença
Set<String> diferenca = new HashSet<>(cores);
diferenca.removeAll(outrasCore);

3. TREEMAP E TREESET

CONCEITO:
- Implementações baseadas em árvore (Red-Black Tree)
- Mantêm elementos ordenados
- Operações O(log n)
- Não permitem elementos null

TREEMAP:
import java.util.TreeMap;

TreeMap<String, Integer> mapa = new TreeMap<>();
mapa.put("zebra", 1);
mapa.put("abelha", 2);
mapa.put("gato", 3);

// Ordem alfabética automática: abelha, gato, zebra
for (String chave : mapa.keySet()) {
    System.out.println(chave);  // abelha, gato, zebra
}

// Métodos específicos
String primeira = mapa.firstKey();         // "abelha"
String ultima = mapa.lastKey();            // "zebra"
String menor = mapa.lowerKey("gato");      // "abelha"
String maior = mapa.higherKey("gato");     // "zebra"

TREESET:
import java.util.TreeSet;

TreeSet<Integer> numeros = new TreeSet<>();
numeros.add(5);
numeros.add(1);
numeros.add(3);
numeros.add(2);

// Ordem natural: 1, 2, 3, 5
for (Integer num : numeros) {
    System.out.println(num);
}

// Métodos específicos
Integer primeiro = numeros.first();        // 1
Integer ultimo = numeros.last();           // 5
Integer menor = numeros.lower(3);          // 2
Integer maior = numeros.higher(3);         // 5

4. QUEUE, DEQUE, STACK

QUEUE (Fila - FIFO):
import java.util.Queue;
import java.util.LinkedList;

Queue<String> fila = new LinkedList<>();

// Adição
fila.offer("primeiro");    // adiciona no final
fila.offer("segundo");
fila.offer("terceiro");

// Remoção
String removido = fila.poll();     // remove do início ("primeiro")
String proximo = fila.peek();      // visualiza sem remover ("segundo")

DEQUE (Double-ended queue):
import java.util.Deque;
import java.util.ArrayDeque;

Deque<String> deque = new ArrayDeque<>();

// Adição
deque.addFirst("início");
deque.addLast("fim");
deque.offerFirst("novo início");
deque.offerLast("novo fim");

// Remoção
String primeiro = deque.removeFirst();
String ultimo = deque.removeLast();

STACK (Pilha - LIFO):
import java.util.Stack;

Stack<String> pilha = new Stack<>();

// Adição
pilha.push("primeiro");
pilha.push("segundo");
pilha.push("terceiro");

// Remoção
String topo = pilha.pop();         // "terceiro"
String proximo = pilha.peek();     // "segundo" (sem remover)

// Verificação
boolean vazia = pilha.isEmpty();
int posicao = pilha.search("primeiro");  // posição do topo (1-based)

RECOMENDAÇÃO MODERNA:
// Prefira Deque ao invés de Stack
Deque<String> pilhaModerna = new ArrayDeque<>();
pilhaModerna.push("elemento");
String elemento = pilhaModerna.pop();

================================================================================
                                  PÁGINA 7
                            TRATAMENTO DE ERROS
================================================================================

1. TRY-CATCH-FINALLY

CONCEITO:
- Mecanismo para tratar exceções
- try: código que pode gerar exceção
- catch: tratamento da exceção
- finally: código que sempre executa

SINTAXE BÁSICA:
try {
    // código que pode gerar exceção
    int resultado = 10 / 0;
} catch (ArithmeticException e) {
    // tratamento específico
    System.out.println("Erro: divisão por zero");
} catch (Exception e) {
    // tratamento genérico
    System.out.println("Erro geral: " + e.getMessage());
} finally {
    // sempre executa (opcional)
    System.out.println("Limpeza de recursos");
}

MÚLTIPLOS CATCHES:
try {
    String texto = null;
    int tamanho = texto.length();        // NullPointerException
    int numero = Integer.parseInt("abc"); // NumberFormatException
} catch (NullPointerException e) {
    System.out.println("Referência nula");
} catch (NumberFormatException e) {
    System.out.println("Formato de número inválido");
} catch (Exception e) {
    System.out.println("Erro não esperado: " + e.getMessage());
}

MULTI-CATCH (Java 7+):
try {
    // código
} catch (IOException | SQLException e) {
    System.out.println("Erro de IO ou SQL: " + e.getMessage());
}

TRY-WITH-RESOURCES (Java 7+):
// Recursos são fechados automaticamente
try (FileReader file = new FileReader("arquivo.txt");
     BufferedReader reader = new BufferedReader(file)) {

    String linha = reader.readLine();
    System.out.println(linha);

} catch (IOException e) {
    System.out.println("Erro ao ler arquivo: " + e.getMessage());
}
// file e reader são fechados automaticamente

2. LANÇAMENTO DE EXCEÇÕES (THROW / THROWS)

THROW:
- Lança uma exceção manualmente
- Usado dentro de métodos

public void sacar(double valor) {
    if (valor <= 0) {
        throw new IllegalArgumentException("Valor deve ser positivo");
    }
    if (valor > saldo) {
        throw new RuntimeException("Saldo insuficiente");
    }
    saldo -= valor;
}

THROWS:
- Declara que o método pode lançar exceções
- Usado na assinatura do método

public void lerArquivo(String nomeArquivo) throws IOException {
    FileReader file = new FileReader(nomeArquivo);
    // código que pode gerar IOException
}

// Quem chama deve tratar
public void exemploUso() {
    try {
        lerArquivo("dados.txt");
    } catch (IOException e) {
        System.out.println("Erro ao ler arquivo");
    }
}

MÚLTIPLAS EXCEÇÕES:
public void metodoComplexo() throws IOException, SQLException, ClassNotFoundException {
    // código que pode gerar várias exceções
}

3. EXCEÇÕES PERSONALIZADAS

CRIAÇÃO:
// Exceção verificada (checked)
public class SaldoInsuficienteException extends Exception {
    private double saldoAtual;
    private double valorSaque;

    public SaldoInsuficienteException(String mensagem, double saldoAtual, double valorSaque) {
        super(mensagem);
        this.saldoAtual = saldoAtual;
        this.valorSaque = valorSaque;
    }

    public double getSaldoAtual() { return saldoAtual; }
    public double getValorSaque() { return valorSaque; }
}

// Exceção não verificada (unchecked)
public class ContaInvalidaException extends RuntimeException {
    public ContaInvalidaException(String mensagem) {
        super(mensagem);
    }

    public ContaInvalidaException(String mensagem, Throwable causa) {
        super(mensagem, causa);
    }
}

USO:
public class ContaBancaria {
    private double saldo;

    public void sacar(double valor) throws SaldoInsuficienteException {
        if (valor > saldo) {
            throw new SaldoInsuficienteException(
                "Saldo insuficiente para saque",
                saldo,
                valor
            );
        }
        saldo -= valor;
    }
}

// Uso da exceção personalizada
try {
    conta.sacar(1000);
} catch (SaldoInsuficienteException e) {
    System.out.println(e.getMessage());
    System.out.println("Saldo atual: " + e.getSaldoAtual());
    System.out.println("Valor tentativa: " + e.getValorSaque());
}

HIERARQUIA DE EXCEÇÕES:
Throwable
├── Error (erros do sistema - não tratar)
│   ├── OutOfMemoryError
│   └── StackOverflowError
└── Exception
    ├── RuntimeException (unchecked)
    │   ├── NullPointerException
    │   ├── IllegalArgumentException
    │   ├── IndexOutOfBoundsException
    │   └── ClassCastException
    └── Checked Exceptions
        ├── IOException
        ├── SQLException
        └── ClassNotFoundException

BOAS PRÁTICAS:
1. Seja específico: catch exceções específicas antes das genéricas
2. Não ignore exceções: sempre trate ou propague
3. Use finally para limpeza de recursos ou try-with-resources
4. Crie exceções personalizadas quando necessário
5. Não use exceções para controle de fluxo normal
6. Log exceções adequadamente
7. Falhe rápido: valide parâmetros no início dos métodos

EXEMPLO COMPLETO:
public class ProcessadorArquivo {
    public void processarArquivo(String nomeArquivo)
            throws ArquivoNaoEncontradoException, ProcessamentoException {

        if (nomeArquivo == null || nomeArquivo.trim().isEmpty()) {
            throw new IllegalArgumentException("Nome do arquivo não pode ser nulo ou vazio");
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(nomeArquivo))) {
            String linha;
            while ((linha = reader.readLine()) != null) {
                processarLinha(linha);
            }
        } catch (FileNotFoundException e) {
            throw new ArquivoNaoEncontradoException("Arquivo não encontrado: " + nomeArquivo, e);
        } catch (IOException e) {
            throw new ProcessamentoException("Erro ao processar arquivo: " + nomeArquivo, e);
        }
    }

    private void processarLinha(String linha) throws ProcessamentoException {
        // lógica de processamento
    }
}

================================================================================
                                  PÁGINA 8
                               BOAS PRÁTICAS
================================================================================

1. PRINCÍPIOS SOLID

S - SINGLE RESPONSIBILITY PRINCIPLE (Responsabilidade Única):
- Uma classe deve ter apenas um motivo para mudar
- Cada classe deve ter uma única responsabilidade

// ❌ Violação - múltiplas responsabilidades
class Usuario {
    private String nome;
    private String email;

    public void salvarNoBanco() { /* código de persistência */ }
    public void enviarEmail() { /* código de email */ }
    public void validarDados() { /* código de validação */ }
}

// ✅ Correto - responsabilidades separadas
class Usuario {
    private String nome;
    private String email;
    // apenas dados e comportamentos relacionados ao usuário
}

class UsuarioRepository {
    public void salvar(Usuario usuario) { /* persistência */ }
}

class EmailService {
    public void enviar(String email, String mensagem) { /* email */ }
}

class UsuarioValidator {
    public boolean validar(Usuario usuario) { /* validação */ }
}

O - OPEN/CLOSED PRINCIPLE (Aberto/Fechado):
- Aberto para extensão, fechado para modificação

// ✅ Usando polimorfismo para extensão
abstract class CalculadoraDesconto {
    public abstract double calcular(double valor);
}

class DescontoCliente extends CalculadoraDesconto {
    @Override
    public double calcular(double valor) {
        return valor * 0.1; // 10% desconto
    }
}

class DescontoVIP extends CalculadoraDesconto {
    @Override
    public double calcular(double valor) {
        return valor * 0.2; // 20% desconto
    }
}

L - LISKOV SUBSTITUTION PRINCIPLE (Substituição de Liskov):
- Objetos de uma superclasse devem ser substituíveis por objetos de suas subclasses

// ✅ Correto - subclasse mantém comportamento esperado
class Retangulo {
    protected int largura, altura;

    public void setLargura(int largura) { this.largura = largura; }
    public void setAltura(int altura) { this.altura = altura; }
    public int getArea() { return largura * altura; }
}

class Quadrado extends Retangulo {
    @Override
    public void setLargura(int largura) {
        this.largura = this.altura = largura;
    }

    @Override
    public void setAltura(int altura) {
        this.largura = this.altura = altura;
    }
}

I - INTERFACE SEGREGATION PRINCIPLE (Segregação de Interface):
- Clientes não devem depender de interfaces que não usam

// ❌ Interface muito grande
interface Trabalhador {
    void trabalhar();
    void comer();
    void dormir();
}

// ✅ Interfaces específicas
interface Trabalhavel {
    void trabalhar();
}

interface Alimentavel {
    void comer();
}

interface Descansavel {
    void dormir();
}

class Humano implements Trabalhavel, Alimentavel, Descansavel {
    // implementa todos
}

class Robo implements Trabalhavel {
    // implementa apenas o que faz sentido
}

D - DEPENDENCY INVERSION PRINCIPLE (Inversão de Dependência):
- Dependa de abstrações, não de implementações concretas

// ❌ Dependência de implementação concreta
class PedidoService {
    private EmailService emailService = new EmailService(); // dependência concreta

    public void processarPedido(Pedido pedido) {
        // processar pedido
        emailService.enviar("Pedido processado");
    }
}

// ✅ Dependência de abstração
interface NotificacaoService {
    void enviar(String mensagem);
}

class PedidoService {
    private NotificacaoService notificacaoService;

    public PedidoService(NotificacaoService notificacaoService) {
        this.notificacaoService = notificacaoService; // injeção de dependência
    }

    public void processarPedido(Pedido pedido) {
        // processar pedido
        notificacaoService.enviar("Pedido processado");
    }
}

2. CLEAN CODE

NOMES SIGNIFICATIVOS:
// ❌ Nomes ruins
int d; // dias
List<int[]> list1;
public void calc(int x, int y);

// ✅ Nomes claros
int diasDesdeModificacao;
List<Produto> produtosAtivos;
public void calcularImpostoSobreVenda(int valorVenda, int percentualImposto);

FUNÇÕES PEQUENAS:
// ✅ Função com responsabilidade única
public boolean isValidEmail(String email) {
    return email != null &&
           email.contains("@") &&
           email.contains(".") &&
           email.length() > 5;
}

EVITAR COMENTÁRIOS DESNECESSÁRIOS:
// ❌ Comentário óbvio
int idade = 25; // define idade como 25

// ✅ Código auto-explicativo
int idadeMinimaPararDirigir = 18;

TRATAMENTO DE ERROS:
// ✅ Falhe rápido
public void processarIdade(int idade) {
    if (idade < 0 || idade > 150) {
        throw new IllegalArgumentException("Idade deve estar entre 0 e 150");
    }
    // processar idade válida
}

3. REFATORAÇÃO

EXTRACT METHOD:
// ❌ Método muito longo
public void processarPedido(Pedido pedido) {
    // validar pedido
    if (pedido == null) throw new IllegalArgumentException();
    if (pedido.getItens().isEmpty()) throw new IllegalArgumentException();

    // calcular total
    double total = 0;
    for (Item item : pedido.getItens()) {
        total += item.getPreco() * item.getQuantidade();
    }

    // aplicar desconto
    if (pedido.getCliente().isVIP()) {
        total *= 0.9;
    }

    // salvar
    repository.salvar(pedido);
}

// ✅ Métodos extraídos
public void processarPedido(Pedido pedido) {
    validarPedido(pedido);
    double total = calcularTotal(pedido);
    total = aplicarDesconto(pedido, total);
    salvarPedido(pedido);
}

private void validarPedido(Pedido pedido) { /* validação */ }
private double calcularTotal(Pedido pedido) { /* cálculo */ }
private double aplicarDesconto(Pedido pedido, double total) { /* desconto */ }
private void salvarPedido(Pedido pedido) { /* persistência */ }

4. DRY, KISS, YAGNI

DRY (Don't Repeat Yourself):
// ❌ Repetição
public void enviarEmailBemVindo(Usuario usuario) {
    String assunto = "Bem-vindo " + usuario.getNome();
    String corpo = "Olá " + usuario.getNome() + ", bem-vindo!";
    emailService.enviar(usuario.getEmail(), assunto, corpo);
}

public void enviarEmailDespedida(Usuario usuario) {
    String assunto = "Até logo " + usuario.getNome();
    String corpo = "Olá " + usuario.getNome() + ", até logo!";
    emailService.enviar(usuario.getEmail(), assunto, corpo);
}

// ✅ Sem repetição
public void enviarEmail(Usuario usuario, String tipoEmail) {
    String assunto = criarAssunto(usuario, tipoEmail);
    String corpo = criarCorpo(usuario, tipoEmail);
    emailService.enviar(usuario.getEmail(), assunto, corpo);
}

KISS (Keep It Simple, Stupid):
// ❌ Complexo desnecessariamente
public boolean isEven(int number) {
    return ((number & 1) == 0) ? true : false;
}

// ✅ Simples
public boolean isEven(int number) {
    return number % 2 == 0;
}

YAGNI (You Aren't Gonna Need It):
- Não implemente funcionalidades até que sejam realmente necessárias
- Evite over-engineering
- Foque no que é necessário agora

================================================================================
