================================================================================
                    GUIA COMPLETO DE JAVA PARA ENTREVISTAS - PARTE 3
================================================================================

                                  PÁGINA 12
                           JAVA AVANÇADO - PARTE 2
================================================================================

4. ANOTAÇÕES (ANNOTATIONS)

CONCEITO:
- Metadados que fornecem informações sobre o código
- Não afetam diretamente a execução
- Processadas em tempo de compilação ou execução

ANOTAÇÕES BUILT-IN:
@Override - indica sobrescrita de método
@Deprecated - marca como obsoleto
@SuppressWarnings - suprime avisos do compilador
@FunctionalInterface - marca interface funcional

EXEMPLO:
public class Exemplo {
    @Override
    public String toString() {
        return "Exemplo";
    }
    
    @Deprecated
    public void metodoAntigo() {
        // método obsoleto
    }
    
    @SuppressWarnings("unchecked")
    public void metodoComWarning() {
        List lista = new ArrayList(); // raw type
    }
}

CRIANDO ANOTAÇÕES PERSONALIZADAS:
// Anotação simples
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Cronometrar {
}

// Anotação com parâmetros
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Auditoria {
    String valor() default "";
    String[] operacoes() default {};
    boolean ativo() default true;
}

// Uso
@Auditoria(valor = "Usuario", operacoes = {"CREATE", "UPDATE"}, ativo = true)
public class UsuarioService {
    
    @Cronometrar
    @Auditoria("buscarPorId")
    public Usuario buscarPorId(Long id) {
        // implementação
    }
}

PROCESSAMENTO DE ANOTAÇÕES:
public class ProcessadorAnotacoes {
    public void processarClasse(Class<?> clazz) {
        // Verificar anotação na classe
        if (clazz.isAnnotationPresent(Auditoria.class)) {
            Auditoria auditoria = clazz.getAnnotation(Auditoria.class);
            System.out.println("Classe auditada: " + auditoria.valor());
        }
        
        // Processar métodos
        for (Method method : clazz.getDeclaredMethods()) {
            if (method.isAnnotationPresent(Cronometrar.class)) {
                System.out.println("Método cronometrado: " + method.getName());
            }
        }
    }
}

5. REFLECTION

CONCEITO:
- Examinar e modificar estrutura e comportamento de classes em tempo de execução
- Acessar campos privados, invocar métodos, criar instâncias
- Usado por frameworks (Spring, Hibernate)

OBTENDO INFORMAÇÕES DE CLASSE:
Class<?> clazz = String.class;
// ou
Class<?> clazz2 = Class.forName("java.lang.String");
// ou
String str = "hello";
Class<?> clazz3 = str.getClass();

// Informações básicas
String nome = clazz.getName();           // java.lang.String
String nomeSimples = clazz.getSimpleName(); // String
Package pacote = clazz.getPackage();

TRABALHANDO COM CAMPOS:
public class Pessoa {
    private String nome;
    public int idade;
}

Class<?> pessoaClass = Pessoa.class;

// Obter todos os campos
Field[] campos = pessoaClass.getDeclaredFields();

// Obter campo específico
Field campoNome = pessoaClass.getDeclaredField("nome");

// Acessar campo privado
Pessoa pessoa = new Pessoa();
campoNome.setAccessible(true); // permite acesso a campo privado
campoNome.set(pessoa, "João");
String nome = (String) campoNome.get(pessoa);

TRABALHANDO COM MÉTODOS:
// Obter todos os métodos
Method[] metodos = pessoaClass.getDeclaredMethods();

// Obter método específico
Method metodo = pessoaClass.getDeclaredMethod("setNome", String.class);

// Invocar método
metodo.invoke(pessoa, "Maria");

CRIANDO INSTÂNCIAS:
// Construtor padrão
Pessoa pessoa1 = (Pessoa) pessoaClass.getDeclaredConstructor().newInstance();

// Construtor com parâmetros
Constructor<Pessoa> construtor = pessoaClass.getDeclaredConstructor(String.class, int.class);
Pessoa pessoa2 = construtor.newInstance("João", 30);

6. ENUMERAÇÕES (ENUMS)

CONCEITO:
- Tipo especial de classe para representar constantes
- Instâncias são criadas automaticamente
- Implicitamente extends Enum<T>

ENUM SIMPLES:
public enum DiaSemana {
    SEGUNDA, TERCA, QUARTA, QUINTA, SEXTA, SABADO, DOMINGO
}

// Uso
DiaSemana hoje = DiaSemana.SEGUNDA;
System.out.println(hoje); // SEGUNDA

// Métodos úteis
DiaSemana[] dias = DiaSemana.values(); // todos os valores
DiaSemana dia = DiaSemana.valueOf("TERCA"); // por string
int posicao = hoje.ordinal(); // posição (0-based)

ENUM COM CAMPOS E MÉTODOS:
public enum Planeta {
    MERCURIO(3.303e+23, 2.4397e6),
    VENUS(4.869e+24, 6.0518e6),
    TERRA(5.976e+24, 6.37814e6),
    MARTE(6.421e+23, 3.3972e6);
    
    private final double massa;   // em quilogramas
    private final double raio;    // em metros
    
    Planeta(double massa, double raio) {
        this.massa = massa;
        this.raio = raio;
    }
    
    public double getMassa() { return massa; }
    public double getRaio() { return raio; }
    
    // Constante gravitacional universal (m3 kg-1 s-2)
    public static final double G = 6.67300E-11;
    
    public double gravidadeSuperficie() {
        return G * massa / (raio * raio);
    }
    
    public double pesoSuperficie(double outraMassa) {
        return outraMassa * gravidadeSuperficie();
    }
}

// Uso
double pesoNaTerra = Planeta.TERRA.pesoSuperficie(80); // 80kg na Terra
double pesoEmMarte = Planeta.MARTE.pesoSuperficie(80); // mesmo peso em Marte

ENUM COM MÉTODOS ABSTRATOS:
public enum Operacao {
    SOMA {
        @Override
        public double calcular(double x, double y) {
            return x + y;
        }
    },
    SUBTRACAO {
        @Override
        public double calcular(double x, double y) {
            return x - y;
        }
    },
    MULTIPLICACAO {
        @Override
        public double calcular(double x, double y) {
            return x * y;
        }
    },
    DIVISAO {
        @Override
        public double calcular(double x, double y) {
            return x / y;
        }
    };
    
    public abstract double calcular(double x, double y);
}

// Uso
double resultado = Operacao.SOMA.calcular(5, 3); // 8

ENUM EM SWITCH:
public String obterTipoDeTrabalho(DiaSemana dia) {
    switch (dia) {
        case SEGUNDA:
        case TERCA:
        case QUARTA:
        case QUINTA:
        case SEXTA:
            return "Dia útil";
        case SABADO:
        case DOMINGO:
            return "Final de semana";
        default:
            throw new IllegalArgumentException("Dia inválido");
    }
}

VANTAGENS DOS ENUMS:
- Type safety
- Namespace próprio
- Podem implementar interfaces
- Podem ter campos e métodos
- Comparação eficiente (==)
- Funcionam bem com switch
- Serialização automática

================================================================================
                                  PÁGINA 13
                      INTEGRAÇÃO E COMUNICAÇÃO - PARTE 1
================================================================================

1. APIs REST USANDO SPRING BOOT

CONCEITO REST:
- Representational State Transfer
- Arquitetura para serviços web
- Usa métodos HTTP (GET, POST, PUT, DELETE)
- Stateless (sem estado)
- Recursos identificados por URLs

CONFIGURAÇÃO BÁSICA:
@SpringBootApplication
public class ApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(ApiApplication.class, args);
    }
}

CONTROLLER REST:
@RestController
@RequestMapping("/api/produtos")
@CrossOrigin(origins = "*") // CORS
public class ProdutoController {
    
    @Autowired
    private ProdutoService produtoService;
    
    // GET /api/produtos
    @GetMapping
    public ResponseEntity<List<ProdutoDTO>> listarTodos() {
        List<ProdutoDTO> produtos = produtoService.listarTodos();
        return ResponseEntity.ok(produtos);
    }
    
    // GET /api/produtos/{id}
    @GetMapping("/{id}")
    public ResponseEntity<ProdutoDTO> buscarPorId(@PathVariable Long id) {
        try {
            ProdutoDTO produto = produtoService.buscarPorId(id);
            return ResponseEntity.ok(produto);
        } catch (ProdutoNaoEncontradoException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    // POST /api/produtos
    @PostMapping
    public ResponseEntity<ProdutoDTO> criar(@Valid @RequestBody CriarProdutoDTO dto) {
        ProdutoDTO produto = produtoService.criar(dto);
        URI location = ServletUriComponentsBuilder
            .fromCurrentRequest()
            .path("/{id}")
            .buildAndExpand(produto.getId())
            .toUri();
        return ResponseEntity.created(location).body(produto);
    }
    
    // PUT /api/produtos/{id}
    @PutMapping("/{id}")
    public ResponseEntity<ProdutoDTO> atualizar(
            @PathVariable Long id, 
            @Valid @RequestBody AtualizarProdutoDTO dto) {
        try {
            ProdutoDTO produto = produtoService.atualizar(id, dto);
            return ResponseEntity.ok(produto);
        } catch (ProdutoNaoEncontradoException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    // DELETE /api/produtos/{id}
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletar(@PathVariable Long id) {
        try {
            produtoService.deletar(id);
            return ResponseEntity.noContent().build();
        } catch (ProdutoNaoEncontradoException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    // GET /api/produtos/buscar?nome=...&categoria=...
    @GetMapping("/buscar")
    public ResponseEntity<List<ProdutoDTO>> buscar(
            @RequestParam(required = false) String nome,
            @RequestParam(required = false) String categoria,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        List<ProdutoDTO> produtos = produtoService.buscar(nome, categoria, pageable);
        return ResponseEntity.ok(produtos);
    }
}

TRATAMENTO DE ERROS GLOBAL:
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ProdutoNaoEncontradoException.class)
    public ResponseEntity<ErrorResponse> handleProdutoNaoEncontrado(
            ProdutoNaoEncontradoException e) {
        ErrorResponse error = new ErrorResponse(
            "PRODUTO_NAO_ENCONTRADO",
            e.getMessage(),
            LocalDateTime.now()
        );
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationErrors(
            MethodArgumentNotValidException e) {
        
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getFieldErrors().forEach(error -> 
            errors.put(error.getField(), error.getDefaultMessage())
        );
        
        ErrorResponse errorResponse = new ErrorResponse(
            "VALIDATION_ERROR",
            "Dados inválidos",
            LocalDateTime.now(),
            errors
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericError(Exception e) {
        ErrorResponse error = new ErrorResponse(
            "INTERNAL_ERROR",
            "Erro interno do servidor",
            LocalDateTime.now()
        );
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}

CONFIGURAÇÃO DE CORS:
@Configuration
public class CorsConfig {
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}

================================================================================
                                  PÁGINA 14
                      INTEGRAÇÃO E COMUNICAÇÃO - PARTE 2
================================================================================

2. SERIALIZAÇÃO JSON (JACKSON, GSON)

JACKSON (Spring Boot padrão):
@Configuration
public class JacksonConfig {
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }
}

ANOTAÇÕES JACKSON:
public class ProdutoDTO {
    @JsonProperty("id")
    private Long id;

    @JsonFormat(pattern = "dd/MM/yyyy HH:mm:ss")
    private LocalDateTime dataCriacao;

    @JsonIgnore
    private String senhaInterna;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String descricao;
}

USO PROGRAMÁTICO:
@Service
public class JsonService {
    @Autowired
    private ObjectMapper objectMapper;

    public String objetoParaJson(Object objeto) throws JsonProcessingException {
        return objectMapper.writeValueAsString(objeto);
    }

    public <T> T jsonParaObjeto(String json, Class<T> classe) throws JsonProcessingException {
        return objectMapper.readValue(json, classe);
    }
}

3. WEBSOCKETS

CONFIGURAÇÃO:
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new ChatWebSocketHandler(), "/chat")
                .setAllowedOrigins("*");
    }
}

HANDLER:
@Component
public class ChatWebSocketHandler extends TextWebSocketHandler {
    private final Set<WebSocketSession> sessions = Collections.synchronizedSet(new HashSet<>());

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        System.out.println("Conexão estabelecida: " + session.getId());
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();

        // Broadcast para todas as sessões
        synchronized (sessions) {
            for (WebSocketSession s : sessions) {
                if (s.isOpen()) {
                    s.sendMessage(new TextMessage("Echo: " + payload));
                }
            }
        }
    }
}

4. CONSUMO DE APIs EXTERNAS

RESTTEMPLATE:
@Service
public class ApiExternaService {
    private final RestTemplate restTemplate = new RestTemplate();
    private final String baseUrl = "https://api.externa.com";

    public UsuarioExternoDTO buscarUsuario(Long id) {
        String url = baseUrl + "/usuarios/" + id;

        try {
            ResponseEntity<UsuarioExternoDTO> response = restTemplate.getForEntity(
                url, UsuarioExternoDTO.class);
            return response.getBody();
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new UsuarioNaoEncontradoException("Usuário não encontrado");
            }
            throw new RuntimeException("Erro ao consultar API externa", e);
        }
    }

    public UsuarioExternoDTO criarUsuario(CriarUsuarioExternoDTO dto) {
        String url = baseUrl + "/usuarios";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("seu-token-aqui");

        HttpEntity<CriarUsuarioExternoDTO> request = new HttpEntity<>(dto, headers);

        ResponseEntity<UsuarioExternoDTO> response = restTemplate.postForEntity(
            url, request, UsuarioExternoDTO.class);

        return response.getBody();
    }
}

WEBCLIENT (Reativo - Recomendado):
@Service
public class ApiExternaWebClientService {
    private final WebClient webClient;

    public ApiExternaWebClientService() {
        this.webClient = WebClient.builder()
            .baseUrl("https://api.externa.com")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();
    }

    public Mono<UsuarioExternoDTO> buscarUsuarioAsync(Long id) {
        return webClient.get()
            .uri("/usuarios/{id}", id)
            .retrieve()
            .onStatus(HttpStatus::is4xxClientError, response -> {
                if (response.statusCode() == HttpStatus.NOT_FOUND) {
                    return Mono.error(new UsuarioNaoEncontradoException("Usuário não encontrado"));
                }
                return Mono.error(new RuntimeException("Erro na API externa"));
            })
            .bodyToMono(UsuarioExternoDTO.class);
    }

    public UsuarioExternoDTO buscarUsuario(Long id) {
        return buscarUsuarioAsync(id).block(); // Bloqueia para uso síncrono
    }
}

================================================================================
                                  PÁGINA 15
                        CONCORRÊNCIA E MULTITHREADING
================================================================================

1. THREADS E RUNNABLE

CRIANDO THREADS:
// Estendendo Thread
class MinhaThread extends Thread {
    @Override
    public void run() {
        for (int i = 0; i < 5; i++) {
            System.out.println("Thread: " + Thread.currentThread().getName() + " - " + i);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}

// Implementando Runnable (preferível)
class MinhaTask implements Runnable {
    @Override
    public void run() {
        for (int i = 0; i < 5; i++) {
            System.out.println("Runnable: " + Thread.currentThread().getName() + " - " + i);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return;
            }
        }
    }
}

// Uso
Thread thread1 = new MinhaThread();
thread1.start();

Thread thread2 = new Thread(new MinhaTask());
thread2.start();

// Com Lambda (Java 8+)
Thread thread3 = new Thread(() -> {
    System.out.println("Thread com lambda: " + Thread.currentThread().getName());
});
thread3.start();

MÉTODOS IMPORTANTES:
thread.start();        // inicia thread
thread.join();         // espera thread terminar
thread.interrupt();    // interrompe thread
Thread.sleep(1000);    // pausa thread atual
Thread.yield();        // cede processamento

2. EXECUTORSERVICE

CONCEITO:
- Framework de alto nível para gerenciar threads
- Pool de threads reutilizáveis
- Melhor controle e performance

TIPOS DE EXECUTORS:
// Thread pool fixo
ExecutorService executor = Executors.newFixedThreadPool(4);

// Thread pool que cresce conforme necessário
ExecutorService executor2 = Executors.newCachedThreadPool();

// Uma única thread
ExecutorService executor3 = Executors.newSingleThreadExecutor();

// Thread pool para tarefas agendadas
ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

USO BÁSICO:
ExecutorService executor = Executors.newFixedThreadPool(3);

// Executar Runnable
executor.execute(() -> {
    System.out.println("Tarefa executada por: " + Thread.currentThread().getName());
});

// Executar Callable (com retorno)
Future<String> future = executor.submit(() -> {
    Thread.sleep(2000);
    return "Resultado da tarefa";
});

try {
    String resultado = future.get(); // bloqueia até completar
    System.out.println(resultado);
} catch (InterruptedException | ExecutionException e) {
    e.printStackTrace();
}

// Sempre fechar o executor
executor.shutdown();

EXEMPLO PRÁTICO:
public class ProcessadorTarefas {
    private final ExecutorService executor = Executors.newFixedThreadPool(5);

    public void processarLista(List<String> itens) {
        List<Future<String>> futures = new ArrayList<>();

        for (String item : itens) {
            Future<String> future = executor.submit(() -> {
                // Simular processamento
                Thread.sleep(1000);
                return "Processado: " + item;
            });
            futures.add(future);
        }

        // Coletar resultados
        for (Future<String> future : futures) {
            try {
                String resultado = future.get();
                System.out.println(resultado);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }
    }

    public void fechar() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
        }
    }
}

3. SYNCHRONIZED, VOLATILE

SYNCHRONIZED:
- Garante acesso exclusivo a um bloco de código
- Previne condições de corrida (race conditions)

public class Contador {
    private int valor = 0;

    // Método sincronizado
    public synchronized void incrementar() {
        valor++;
    }

    // Bloco sincronizado
    public void decrementar() {
        synchronized (this) {
            valor--;
        }
    }

    public synchronized int getValor() {
        return valor;
    }
}

VOLATILE:
- Garante visibilidade entre threads
- Não garante atomicidade

public class Flag {
    private volatile boolean ativo = true;

    public void parar() {
        ativo = false; // mudança visível para todas as threads
    }

    public void executar() {
        while (ativo) {
            // fazer trabalho
        }
    }
}

4. COMPLETABLEFUTURE

CONCEITO:
- Programação assíncrona e reativa
- Composição de operações assíncronas
- Introduzido no Java 8

CRIAÇÃO:
// Criar CompletableFuture
CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
    try {
        Thread.sleep(1000);
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
    }
    return "Resultado 1";
});

// Executar ação assíncrona sem retorno
CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
    System.out.println("Executando tarefa assíncrona");
});

COMPOSIÇÃO:
CompletableFuture<String> resultado = CompletableFuture
    .supplyAsync(() -> "Hello")
    .thenApply(s -> s + " World")
    .thenApply(String::toUpperCase);

System.out.println(resultado.get()); // "HELLO WORLD"

COMBINAÇÃO:
CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> "Hello");
CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> "World");

CompletableFuture<String> combinado = future1.thenCombine(future2, (s1, s2) -> s1 + " " + s2);

System.out.println(combinado.get()); // "Hello World"

TRATAMENTO DE ERROS:
CompletableFuture<String> futureComErro = CompletableFuture
    .supplyAsync(() -> {
        if (Math.random() > 0.5) {
            throw new RuntimeException("Erro simulado");
        }
        return "Sucesso";
    })
    .exceptionally(throwable -> {
        System.out.println("Erro capturado: " + throwable.getMessage());
        return "Valor padrão";
    });

================================================================================
