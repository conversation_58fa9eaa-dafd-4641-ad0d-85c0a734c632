# GUIA COMPLETO DE JAVA PARA ENTREVISTAS TÉCNICAS

---

## ÍNDICE

**PÁGINA 1: FUNDAMENTOS DE JAVA - EVOLUÇÃO E VERSÕES**
- Diferenças detalhadas entre versões Java 6 até 23
- Recursos importantes de cada versão
- Compatibilidade e migração

**PÁGINA 2: SINTAXE BÁSICA E TIPOS DE DADOS**
- Variáveis, operadores e controle de fluxo
- Tipos primitivos vs referência
- Boxing/Unboxing e performance

**PÁGINA 3: CLASSES, OBJETOS E MÉTODOS**
- Estrutura de classes em profundidade
- Ciclo de vida de objetos
- Métodos: sobrecarga, sobrescrita e polimorfismo

**PÁGINA 4: MODIFICADORES DE ACESSO E PACOTES**
- Encapsulamento e visibilidade
- Organização de código em pacotes
- Boas práticas de estruturação

**PÁGINA 5-6: PROGRAMAÇÃO ORIENTADA A OBJETOS**
- Os 4 pilares da POO explicados em detalhes
- Implementação prática de cada conceito
- Padrões e anti-padrões

**PÁGINA 7-8: HERANÇA E POLIMORFISMO AVANÇADO**
- Hierarquias de classes complexas
- Polimorfismo em tempo de compilação vs execução
- Casos de uso avançados

**PÁGINA 9-10: INTERFACES E CLASSES ABSTRATAS**
- Diferenças conceituais e práticas
- Quando usar cada uma
- Evolução das interfaces (Java 8+)

**PÁGINA 11-13: COLEÇÕES E ESTRUTURAS DE DADOS**
- Framework Collections completo
- Performance e casos de uso
- Implementações customizadas

**PÁGINA 14-15: TRATAMENTO DE EXCEÇÕES**
- Hierarquia completa de exceções
- Estratégias de tratamento
- Exceções personalizadas e boas práticas

**PÁGINA 16-17: GENERICS AVANÇADO**
- Type erasure e limitações
- Wildcards e bounded types
- Casos complexos e armadilhas

**PÁGINA 18-19: PROGRAMAÇÃO FUNCIONAL**
- Lambda expressions em profundidade
- Streams API avançada
- Method references e functional interfaces

**PÁGINA 20-21: CONCORRÊNCIA E MULTITHREADING**
- Thread safety e sincronização
- Concurrent collections
- CompletableFuture e programação assíncrona

**PÁGINA 22-23: JAVA AVANÇADO**
- Reflection e metaprogramming
- Annotations e processamento
- Memory model e garbage collection

**PÁGINA 24-25: SPRING FRAMEWORK**
- Injeção de dependência
- Spring Boot e auto-configuração
- Padrões arquiteturais

**PÁGINA 26-27: TESTES E QUALIDADE**
- TDD e estratégias de teste
- Mocking e test doubles
- Cobertura e métricas

**PÁGINA 28-29: FERRAMENTAS E ECOSSISTEMA**
- Build tools (Maven/Gradle)
- CI/CD e DevOps
- Monitoramento e observabilidade

**PÁGINA 30: BOAS PRÁTICAS E PADRÕES**
- SOLID principles em detalhes
- Design patterns essenciais
- Code review e refactoring

---

## PÁGINA 1: FUNDAMENTOS DE JAVA - EVOLUÇÃO E VERSÕES

### HISTÓRIA E EVOLUÇÃO DO JAVA

Java foi criado pela Sun Microsystems em 1995, com o objetivo de ser uma linguagem "write once, run anywhere" (WORA). A linguagem evoluiu significativamente ao longo dos anos, com cada versão trazendo melhorias importantes.

### JAVA 6 (2006) - MUSTANG
**Principais características:**
- **Scripting API**: Integração com linguagens de script como JavaScript
- **Compiler API**: Possibilidade de compilar código Java programaticamente
- **Pluggable Annotations**: Processamento de anotações em tempo de compilação
- **JDBC 4.0**: Melhorias na API de banco de dados
- **JAX-WS**: Web services mais robustos

**Exemplo de Scripting API:**
```java
ScriptEngineManager manager = new ScriptEngineManager();
ScriptEngine engine = manager.getEngineByName("JavaScript");
engine.eval("print('Hello from JavaScript in Java!')");
```

**Impacto:** Esta versão estabeleceu Java como plataforma enterprise robusta, com melhor integração com outras tecnologias.

### JAVA 7 (2011) - DOLPHIN
**Principais características:**

**1. Try-with-resources (AutoCloseable):**
```java
// Antes do Java 7
FileInputStream fis = null;
try {
    fis = new FileInputStream("file.txt");
    // usar fis
} finally {
    if (fis != null) {
        try {
            fis.close();
        } catch (IOException e) {
            // tratar exceção
        }
    }
}

// Java 7+
try (FileInputStream fis = new FileInputStream("file.txt")) {
    // usar fis - fechamento automático
} catch (IOException e) {
    // tratar exceção
}
```

**2. Diamond Operator:**
```java
// Antes
Map<String, List<String>> map = new HashMap<String, List<String>>();

// Java 7+
Map<String, List<String>> map = new HashMap<>();
```

**3. Switch com Strings:**
```java
String day = "MONDAY";
switch (day) {
    case "MONDAY":
        System.out.println("Start of work week");
        break;
    case "FRIDAY":
        System.out.println("TGIF!");
        break;
    default:
        System.out.println("Regular day");
}
```

**4. Multi-catch:**
```java
try {
    // código que pode lançar múltiplas exceções
} catch (IOException | SQLException e) {
    // tratar ambas as exceções da mesma forma
    logger.error("Database or IO error", e);
}
```

**5. Numeric literals com underscore:**
```java
int million = 1_000_000;
long creditCardNumber = 1234_5678_9012_3456L;
float pi = 3.14_15F;
```

### JAVA 8 (2014) - REVOLUÇÃO FUNCIONAL
Esta é considerada a versão mais revolucionária desde o Java 5.

**1. Lambda Expressions:**
```java
// Comparação de sintaxes
// Antes do Java 8
Collections.sort(list, new Comparator<String>() {
    @Override
    public int compare(String a, String b) {
        return a.compareTo(b);
    }
});

// Java 8+
Collections.sort(list, (a, b) -> a.compareTo(b));
// ou ainda mais simples
Collections.sort(list, String::compareTo);
```

**2. Stream API:**
```java
List<String> names = Arrays.asList("Alice", "Bob", "Charlie", "David");

// Operações funcionais
List<String> result = names.stream()
    .filter(name -> name.length() > 3)
    .map(String::toUpperCase)
    .sorted()
    .collect(Collectors.toList());

// Operações paralelas
long count = names.parallelStream()
    .filter(name -> name.startsWith("A"))
    .count();
```

**3. Optional:**
```java
// Evitar NullPointerException
Optional<String> optional = Optional.ofNullable(getString());

// Uso funcional
optional.ifPresent(System.out::println);

String result = optional
    .filter(s -> s.length() > 5)
    .map(String::toUpperCase)
    .orElse("DEFAULT");
```

**4. Interface com métodos default:**
```java
public interface Vehicle {
    // Método abstrato tradicional
    void start();
    
    // Método default - implementação padrão
    default void stop() {
        System.out.println("Vehicle stopped");
    }
    
    // Método estático
    static void checkEngine() {
        System.out.println("Engine check completed");
    }
}
```

**5. Novo Date/Time API:**
```java
// API antiga (problemática)
Date date = new Date();
Calendar cal = Calendar.getInstance();

// Nova API (Java 8+)
LocalDate today = LocalDate.now();
LocalDateTime now = LocalDateTime.now();
ZonedDateTime zonedNow = ZonedDateTime.now();

// Operações imutáveis
LocalDate nextWeek = today.plusWeeks(1);
LocalDate lastMonth = today.minusMonths(1);

// Formatação
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
String formattedDate = today.format(formatter);
```

### JAVA 9 (2017) - MODULARIDADE
**1. Module System (Project Jigsaw):**
```java
// module-info.java
module com.example.myapp {
    requires java.base;
    requires java.logging;
    exports com.example.myapp.api;
    provides com.example.myapp.spi.Service 
        with com.example.myapp.impl.ServiceImpl;
}
```

**2. JShell - REPL:**
```bash
$ jshell
jshell> int x = 10
x ==> 10
jshell> System.out.println("Hello " + x)
Hello 10
```

**3. Factory Methods para Collections:**
```java
// Métodos convenientes para criar coleções imutáveis
List<String> list = List.of("a", "b", "c");
Set<String> set = Set.of("x", "y", "z");
Map<String, Integer> map = Map.of("key1", 1, "key2", 2);
```

**4. Stream API melhorada:**
```java
// takeWhile e dropWhile
Stream.of(1, 2, 3, 4, 5, 6)
    .takeWhile(n -> n < 4)  // [1, 2, 3]
    .forEach(System.out::println);

// ofNullable
Stream.ofNullable(getNullableValue())
    .forEach(System.out::println);
```

### JAVA 10 (2018) - INFERÊNCIA DE TIPOS
**1. Local Variable Type Inference (var):**
```java
// Antes
Map<String, List<Integer>> map = new HashMap<String, List<Integer>>();
List<String> list = new ArrayList<String>();

// Java 10+
var map = new HashMap<String, List<Integer>>();
var list = new ArrayList<String>();
var stream = list.stream();

// Limitações do var
// var x; // ERRO: deve ser inicializada
// var lambda = () -> {}; // ERRO: não pode inferir tipo de lambda
```

**2. Garbage Collector melhorado:**
- G1GC tornou-se o coletor padrão
- Melhor performance para aplicações com heap grande

### JAVA 11 (2018) - LTS (Long Term Support)
**1. Novos métodos String:**
```java
String text = "  Hello World  ";

// isBlank() - verifica se string é vazia ou só espaços
boolean blank = "   ".isBlank(); // true
boolean empty = "".isBlank();    // true

// strip() - remove espaços (melhor que trim para Unicode)
String stripped = text.strip(); // "Hello World"

// lines() - divide string em linhas
"Line1\nLine2\nLine3".lines()
    .forEach(System.out::println);

// repeat() - repete string
String repeated = "Java ".repeat(3); // "Java Java Java "
```

**2. HttpClient nativo:**
```java
// Cliente HTTP moderno e assíncrono
HttpClient client = HttpClient.newHttpClient();

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.example.com/data"))
    .header("Content-Type", "application/json")
    .GET()
    .build();

// Síncrono
HttpResponse<String> response = client.send(request, 
    HttpResponse.BodyHandlers.ofString());

// Assíncrono
CompletableFuture<HttpResponse<String>> futureResponse = 
    client.sendAsync(request, HttpResponse.BodyHandlers.ofString());
```

**3. Executar arquivos Java diretamente:**
```bash
# Não precisa mais compilar explicitamente
java HelloWorld.java
```

### JAVA 12-16 (2019-2021) - INOVAÇÕES INCREMENTAIS
**Java 12:**
- Switch Expressions (Preview)
- Compact Number Formatting

**Java 13:**
- Text Blocks (Preview)
```java
String html = """
    <html>
        <body>
            <h1>Hello World</h1>
        </body>
    </html>
    """;
```

**Java 14:**
- Pattern Matching para instanceof (Preview)
- Records (Preview)

**Java 15:**
- Text Blocks (Final)
- Sealed Classes (Preview)

**Java 16:**
- Records (Final)
- Pattern Matching para instanceof (Final)

### JAVA 17 (2021) - LTS ATUAL
**1. Records (Final):**
```java
// Classe tradicional
public class PersonOld {
    private final String name;
    private final int age;
    
    public PersonOld(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() { return name; }
    public int getAge() { return age; }
    
    @Override
    public boolean equals(Object obj) { /* implementação */ }
    @Override
    public int hashCode() { /* implementação */ }
    @Override
    public String toString() { /* implementação */ }
}

// Record (Java 17)
public record Person(String name, int age) {
    // Construtor, getters, equals, hashCode, toString gerados automaticamente
    
    // Construtor customizado (opcional)
    public Person {
        if (age < 0) {
            throw new IllegalArgumentException("Age cannot be negative");
        }
    }
    
    // Métodos adicionais (opcional)
    public boolean isAdult() {
        return age >= 18;
    }
}
```

**2. Sealed Classes:**
```java
// Define hierarquia fechada de classes
public sealed class Shape 
    permits Circle, Rectangle, Triangle {
}

public final class Circle extends Shape {
    private final double radius;
    // implementação
}

public final class Rectangle extends Shape {
    private final double width, height;
    // implementação
}

public non-sealed class Triangle extends Shape {
    // pode ser estendida por outras classes
}
```

**3. Pattern Matching para instanceof:**
```java
// Antes
if (obj instanceof String) {
    String str = (String) obj;
    System.out.println(str.toUpperCase());
}

// Java 17+
if (obj instanceof String str) {
    System.out.println(str.toUpperCase());
}

// Com pattern matching mais complexo
public String formatValue(Object obj) {
    return switch (obj) {
        case Integer i -> "Integer: " + i;
        case String s -> "String: " + s.toUpperCase();
        case null -> "null value";
        default -> "Unknown type";
    };
}
```

### JAVA 18-21 (2022-2023) - FUTURO
**Java 18:**
- UTF-8 como charset padrão
- Simple Web Server
- Code Snippets em JavaDoc

**Java 19:**
- Virtual Threads (Preview) - Project Loom
- Pattern Matching para Switch (Preview)

**Java 20:**
- Scoped Values (Preview)
- Record Patterns (Preview)

**Java 21 (2023) - LTS:**
**1. Virtual Threads (Final):**
```java
// Thread tradicional (pesada)
Thread thread = new Thread(() -> {
    // trabalho
});

// Virtual Thread (leve)
Thread virtualThread = Thread.ofVirtual().start(() -> {
    // trabalho
});

// Executor com virtual threads
try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
    for (int i = 0; i < 1_000_000; i++) {
        executor.submit(() -> {
            // pode criar milhões de virtual threads
        });
    }
}
```

**2. String Templates (Preview):**
```java
String name = "World";
int count = 42;

// Template string
String message = STR."Hello \{name}! Count: \{count}";
```

### COMPATIBILIDADE E MIGRAÇÃO

**Backward Compatibility:**
- Java mantém forte compatibilidade com versões anteriores
- Código Java 8 geralmente roda em Java 17+ sem modificações
- Algumas APIs foram removidas (principalmente internas)

**Estratégias de Migração:**
1. **Incremental**: Atualizar uma versão por vez
2. **Direct Jump**: Pular para LTS mais recente
3. **Testing**: Usar ferramentas como jdeps para análise de dependências

**Ferramentas de Migração:**
```bash
# Analisar dependências
jdeps --jdk-internals myapp.jar

# Verificar módulos necessários
jdeps --print-module-deps myapp.jar
```

---

## PÁGINA 2: SINTAXE BÁSICA E TIPOS DE DADOS

### VARIÁVEIS E DECLARAÇÕES

**Tipos de Variáveis:**
1. **Variáveis de Instância (Instance Variables)**
2. **Variáveis de Classe (Static Variables)**
3. **Variáveis Locais (Local Variables)**
4. **Parâmetros (Parameters)**

```java
public class VariableExample {
    // Variável de instância
    private String instanceVar = "Instance";

    // Variável de classe (static)
    private static int classVar = 100;

    public void method(String parameter) { // Parâmetro
        // Variável local
        int localVar = 10;

        // Variável local com inicialização tardia
        final String constant;
        if (parameter != null) {
            constant = parameter.toUpperCase();
        } else {
            constant = "DEFAULT";
        }
    }
}
```

**Regras de Nomenclatura:**
- Deve começar com letra, $ ou _
- Pode conter letras, dígitos, $ e _
- Case-sensitive
- Não pode ser palavra reservada

```java
// Válidos
int age;
String firstName;
double $price;
boolean _isActive;
int value123;

// Inválidos
// int 123value;  // não pode começar com número
// String class;  // palavra reservada
// double my-var; // hífen não permitido
```

### TIPOS PRIMITIVOS EM DETALHES

**1. Tipos Inteiros:**
```java
// byte: 8 bits, -128 a 127
byte b = 127;
byte overflow = (byte) 128; // -128 (overflow)

// short: 16 bits, -32,768 a 32,767
short s = 32767;

// int: 32 bits, -2^31 a 2^31-1 (padrão para literais inteiros)
int i = 2_147_483_647; // valor máximo
int binary = 0b1010;   // literal binário (10 em decimal)
int octal = 0755;      // literal octal (493 em decimal)
int hex = 0xFF;        // literal hexadecimal (255 em decimal)

// long: 64 bits, -2^63 a 2^63-1
long l = 9_223_372_036_854_775_807L; // sufixo L obrigatório
```

**2. Tipos de Ponto Flutuante:**
```java
// float: 32 bits, precisão simples (IEEE 754)
float f = 3.14159f; // sufixo f obrigatório
float scientific = 1.23e-4f; // notação científica

// double: 64 bits, precisão dupla (padrão para literais decimais)
double d = 3.141592653589793;
double large = 1.7976931348623157e+308; // valor máximo

// Problemas de precisão
System.out.println(0.1 + 0.2); // 0.30000000000000004
// Solução: usar BigDecimal para cálculos monetários
```

**3. Tipo Caractere:**
```java
// char: 16 bits, Unicode UTF-16
char c = 'A';
char unicode = '\u0041'; // 'A' em Unicode
char newline = '\n';     // caractere de escape
char tab = '\t';

// Operações aritméticas com char
char a = 'A';
char b = (char) (a + 1); // 'B'
int asciiValue = a;      // 65
```

**4. Tipo Booleano:**
```java
// boolean: true ou false (não é número como em C/C++)
boolean flag = true;
boolean result = (10 > 5); // true

// Não pode ser convertido para int
// int x = flag; // ERRO de compilação
```

### TIPOS DE REFERÊNCIA

**Diferenças Fundamentais:**
```java
// Tipos primitivos: valor armazenado diretamente
int x = 10;
int y = x;    // copia o valor
x = 20;       // y ainda é 10

// Tipos de referência: referência ao objeto
StringBuilder sb1 = new StringBuilder("Hello");
StringBuilder sb2 = sb1; // copia a referência
sb1.append(" World");    // sb2 também vê a mudança
System.out.println(sb2); // "Hello World"
```

**Wrapper Classes e Autoboxing:**
```java
// Wrapper classes para tipos primitivos
Integer intObj = Integer.valueOf(10);
Double doubleObj = Double.valueOf(3.14);
Boolean boolObj = Boolean.valueOf(true);

// Autoboxing (Java 5+)
Integer auto = 10;        // int -> Integer automaticamente
int primitive = auto;     // Integer -> int automaticamente

// Cache de Integer (-128 a 127)
Integer a = 127;
Integer b = 127;
System.out.println(a == b); // true (mesmo objeto)

Integer c = 128;
Integer d = 128;
System.out.println(c == d); // false (objetos diferentes)
System.out.println(c.equals(d)); // true (valores iguais)
```

**Performance Considerations:**
```java
// Primitivos são mais eficientes
int[] primitiveArray = new int[1000000];    // 4MB
Integer[] objectArray = new Integer[1000000]; // ~16MB + overhead

// Autoboxing pode criar objetos desnecessários
Integer sum = 0;
for (int i = 0; i < 1000; i++) {
    sum += i; // cria novo Integer a cada iteração (ineficiente)
}

// Melhor usar primitivo
int sum2 = 0;
for (int i = 0; i < 1000; i++) {
    sum2 += i; // operação primitiva (eficiente)
}
```

### OPERADORES EM DETALHES

**1. Operadores Aritméticos:**
```java
int a = 10, b = 3;

// Básicos
int sum = a + b;      // 13
int diff = a - b;     // 7
int product = a * b;  // 30
int quotient = a / b; // 3 (divisão inteira)
int remainder = a % b; // 1

// Overflow e Underflow
int max = Integer.MAX_VALUE;
int overflow = max + 1; // -2147483648 (overflow silencioso)

// Math.addExact para detectar overflow
try {
    int safe = Math.addExact(max, 1);
} catch (ArithmeticException e) {
    System.out.println("Overflow detectado!");
}

// Operadores de incremento/decremento
int x = 5;
int preInc = ++x;  // x=6, preInc=6 (incrementa depois retorna)
int postInc = x++; // x=7, postInc=6 (retorna depois incrementa)
```

---
