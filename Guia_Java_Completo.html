<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guia Completo de Java para Entrevistas Técnicas</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
            margin-bottom: 40px;
        }
        h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
        }
        h3 {
            color: #2980b9;
            margin-top: 30px;
        }
        h4 {
            color: #8e44ad;
            margin-top: 25px;
        }
        .page-break {
            page-break-before: always;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
        }
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .highlight {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .important {
            background-color: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .tip {
            background-color: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f39c12;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .index {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .index ul {
            list-style-type: none;
            padding-left: 0;
        }
        .index li {
            padding: 5px 0;
            border-bottom: 1px dotted #bdc3c7;
        }
        .index a {
            text-decoration: none;
            color: #2980b9;
        }
        .index a:hover {
            color: #3498db;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .concept-box {
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #ebf3fd;
        }
        .example-box {
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #eafaf1;
        }
        .difference-box {
            border: 2px solid #e74c3c;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #fdf2f2;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GUIA COMPLETO DE JAVA PARA ENTREVISTAS TÉCNICAS</h1>
        
        <div class="index">
            <h2>ÍNDICE DETALHADO</h2>
            <ul>
                <li><a href="#fundamentos">PÁGINA 1-2: Fundamentos de Java - Evolução e Versões</a></li>
                <li><a href="#sintaxe">PÁGINA 3-4: Sintaxe Básica e Tipos de Dados</a></li>
                <li><a href="#classes">PÁGINA 5-6: Classes, Objetos e Métodos</a></li>
                <li><a href="#modificadores">PÁGINA 7: Modificadores de Acesso e Pacotes</a></li>
                <li><a href="#poo">PÁGINA 8-10: Programação Orientada a Objetos</a></li>
                <li><a href="#heranca">PÁGINA 11-12: Herança e Polimorfismo Avançado</a></li>
                <li><a href="#interfaces">PÁGINA 13-14: Interfaces e Classes Abstratas</a></li>
                <li><a href="#colecoes">PÁGINA 15-17: Coleções e Estruturas de Dados</a></li>
                <li><a href="#excecoes">PÁGINA 18-19: Tratamento de Exceções</a></li>
                <li><a href="#generics">PÁGINA 20-21: Generics Avançado</a></li>
                <li><a href="#funcional">PÁGINA 22-23: Programação Funcional</a></li>
                <li><a href="#concorrencia">PÁGINA 24-25: Concorrência e Multithreading</a></li>
                <li><a href="#avancado">PÁGINA 26-27: Java Avançado</a></li>
                <li><a href="#spring">PÁGINA 28-29: Spring Framework</a></li>
                <li><a href="#testes">PÁGINA 30-31: Testes e Qualidade</a></li>
                <li><a href="#ferramentas">PÁGINA 32-33: Ferramentas e Ecossistema</a></li>
                <li><a href="#padroes">PÁGINA 34-35: Boas Práticas e Padrões</a></li>
            </ul>
        </div>

        <div class="page-break" id="fundamentos">
            <h2>PÁGINA 1-2: FUNDAMENTOS DE JAVA - EVOLUÇÃO E VERSÕES</h2>
            
            <div class="concept-box">
                <h3>🎯 CONCEITOS FUNDAMENTAIS</h3>
                <p><strong>Java</strong> é uma linguagem de programação orientada a objetos, criada pela Sun Microsystems em 1995. Principais características:</p>
                <ul>
                    <li><strong>WORA (Write Once, Run Anywhere):</strong> Código compilado roda em qualquer plataforma com JVM</li>
                    <li><strong>Gerenciamento automático de memória:</strong> Garbage Collection</li>
                    <li><strong>Fortemente tipada:</strong> Verificação de tipos em tempo de compilação</li>
                    <li><strong>Segura:</strong> Não permite acesso direto à memória</li>
                    <li><strong>Multithreading nativo:</strong> Suporte built-in para programação concorrente</li>
                </ul>
            </div>

            <h3>📈 EVOLUÇÃO DAS VERSÕES JAVA</h3>

            <h4>JAVA 8 (2014) - A REVOLUÇÃO FUNCIONAL</h4>
            <div class="important">
                <strong>Marco mais importante desde Java 5!</strong> Introduziu programação funcional ao Java.
            </div>

            <div class="example-box">
                <h4>Lambda Expressions - Transformação Radical</h4>
                <div class="code-block">
// ANTES do Java 8 - Código verboso
List&lt;String&gt; names = Arrays.asList("Alice", "Bob", "Charlie");
Collections.sort(names, new Comparator&lt;String&gt;() {
    @Override
    public int compare(String a, String b) {
        return a.compareTo(b);
    }
});

// JAVA 8+ - Código conciso e funcional
names.sort((a, b) -&gt; a.compareTo(b));
// Ainda mais conciso
names.sort(String::compareTo);

// Processamento funcional com Streams
List&lt;String&gt; result = names.stream()
    .filter(name -&gt; name.length() &gt; 3)
    .map(String::toUpperCase)
    .sorted()
    .collect(Collectors.toList());
                </div>
            </div>

            <div class="example-box">
                <h4>Optional - Eliminando NullPointerException</h4>
                <div class="code-block">
// Problema clássico - NullPointerException
public String getCustomerName(Long id) {
    Customer customer = findCustomer(id); // pode retornar null
    return customer.getName().toUpperCase(); // 💥 NPE se customer for null
}

// Solução com Optional (Java 8+)
public Optional&lt;String&gt; getCustomerName(Long id) {
    return findCustomer(id)
        .map(Customer::getName)
        .map(String::toUpperCase);
}

// Uso seguro
Optional&lt;String&gt; name = getCustomerName(123L);
name.ifPresent(System.out::println);
String result = name.orElse("Cliente não encontrado");
                </div>
            </div>

            <div class="example-box">
                <h4>Nova API de Data/Hora - Thread-Safe e Imutável</h4>
                <div class="code-block">
// API antiga (problemática)
Date date = new Date(); // mutável, não thread-safe
SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy"); // não thread-safe

// Nova API (Java 8+) - Imutável e thread-safe
LocalDate today = LocalDate.now();
LocalDateTime now = LocalDateTime.now();
ZonedDateTime zonedNow = ZonedDateTime.now(ZoneId.of("America/Sao_Paulo"));

// Operações imutáveis
LocalDate nextWeek = today.plusWeeks(1);
LocalDate lastMonth = today.minusMonths(1);
LocalDate birthday = LocalDate.of(1990, Month.JANUARY, 15);

// Formatação thread-safe
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
String formattedDate = today.format(formatter);

// Cálculos entre datas
long daysBetween = ChronoUnit.DAYS.between(birthday, today);
Period age = Period.between(birthday, today);
                </div>
            </div>

            <h4>JAVA 11 (2018) - LTS (Long Term Support)</h4>
            <div class="tip">
                <strong>Versão LTS recomendada para produção.</strong> Suporte estendido até 2026.
            </div>

            <div class="example-box">
                <h4>Novos Métodos String - Mais Poder e Simplicidade</h4>
                <div class="code-block">
String text = "  Hello World  \n\n";

// isBlank() - verifica se string é vazia ou só espaços em branco
boolean blank1 = "   ".isBlank();     // true
boolean blank2 = "".isBlank();        // true  
boolean blank3 = "Hello".isBlank();   // false

// strip() - remove espaços (melhor que trim() para Unicode)
String stripped = text.strip();      // "Hello World"
String stripLeading = text.stripLeading();   // "Hello World  \n\n"
String stripTrailing = text.stripTrailing(); // "  Hello World"

// lines() - divide string em linhas (retorna Stream)
text.lines()
    .filter(line -&gt; !line.isBlank())
    .map(String::trim)
    .forEach(System.out::println);

// repeat() - repete string N vezes
String separator = "=".repeat(50);   // "=================================================="
String padding = " ".repeat(10);     // "          "
                </div>
            </div>

            <div class="example-box">
                <h4>HttpClient Nativo - Moderno e Assíncrono</h4>
                <div class="code-block">
// Cliente HTTP moderno (substitui bibliotecas externas)
HttpClient client = HttpClient.newBuilder()
    .connectTimeout(Duration.ofSeconds(10))
    .build();

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.github.com/users/octocat"))
    .header("Accept", "application/json")
    .header("User-Agent", "Java-HttpClient")
    .GET()
    .build();

// Requisição síncrona
HttpResponse&lt;String&gt; response = client.send(request, 
    HttpResponse.BodyHandlers.ofString());

System.out.println("Status: " + response.statusCode());
System.out.println("Body: " + response.body());

// Requisição assíncrona
CompletableFuture&lt;HttpResponse&lt;String&gt;&gt; futureResponse = 
    client.sendAsync(request, HttpResponse.BodyHandlers.ofString());

futureResponse
    .thenApply(HttpResponse::body)
    .thenAccept(System.out::println)
    .join();
                </div>
            </div>

            <h4>JAVA 17 (2021) - LTS ATUAL</h4>
            <div class="important">
                <strong>Versão LTS mais recente!</strong> Recomendada para novos projetos.
            </div>

            <div class="example-box">
                <h4>Records - Classes de Dados Simplificadas</h4>
                <div class="code-block">
// Classe tradicional (muito código boilerplate)
public class PersonOld {
    private final String name;
    private final int age;
    private final String email;
    
    public PersonOld(String name, int age, String email) {
        this.name = name;
        this.age = age;
        this.email = email;
    }
    
    public String getName() { return name; }
    public int getAge() { return age; }
    public String getEmail() { return email; }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PersonOld person = (PersonOld) obj;
        return age == person.age && 
               Objects.equals(name, person.name) && 
               Objects.equals(email, person.email);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, age, email);
    }
    
    @Override
    public String toString() {
        return "PersonOld{name='" + name + "', age=" + age + ", email='" + email + "'}";
    }
}

// Record (Java 17) - Tudo gerado automaticamente!
public record Person(String name, int age, String email) {
    // Construtor, getters, equals, hashCode, toString gerados automaticamente
    
    // Validação no construtor compacto
    public Person {
        if (age &lt; 0) {
            throw new IllegalArgumentException("Idade não pode ser negativa");
        }
        if (email == null || !email.contains("@")) {
            throw new IllegalArgumentException("Email inválido");
        }
    }
    
    // Métodos adicionais
    public boolean isAdult() {
        return age &gt;= 18;
    }
    
    public String getDisplayName() {
        return name + " (" + age + " anos)";
    }
}

// Uso
Person person = new Person("João", 30, "<EMAIL>");
System.out.println(person.name());     // João (getter automático)
System.out.println(person.isAdult());  // true
System.out.println(person);            // Person[name=João, age=30, email=<EMAIL>]
                </div>
            </div>

            <div class="example-box">
                <h4>Pattern Matching para instanceof</h4>
                <div class="code-block">
// Antes - Cast manual necessário
public String formatValue(Object obj) {
    if (obj instanceof String) {
        String str = (String) obj;  // cast manual
        return "String: " + str.toUpperCase();
    } else if (obj instanceof Integer) {
        Integer num = (Integer) obj; // cast manual
        return "Number: " + (num * 2);
    }
    return "Unknown";
}

// Java 17+ - Pattern matching (cast automático)
public String formatValue(Object obj) {
    if (obj instanceof String str) {        // cast automático para 'str'
        return "String: " + str.toUpperCase();
    } else if (obj instanceof Integer num) { // cast automático para 'num'
        return "Number: " + (num * 2);
    }
    return "Unknown";
}

// Com switch expression (ainda mais elegante)
public String formatValue(Object obj) {
    return switch (obj) {
        case String str -&gt; "String: " + str.toUpperCase();
        case Integer num -&gt; "Number: " + (num * 2);
        case Double d when d &gt; 0 -&gt; "Positive double: " + d;
        case null -&gt; "Null value";
        default -&gt; "Unknown type: " + obj.getClass().getSimpleName();
    };
}
                </div>
            </div>
        </div>

        <div class="page-break" id="sintaxe">
            <h2>PÁGINA 3-4: SINTAXE BÁSICA E TIPOS DE DADOS</h2>
            
            <div class="concept-box">
                <h3>🎯 TIPOS DE VARIÁVEIS EM JAVA</h3>
                <p>Java possui 4 tipos de variáveis, cada uma com escopo e ciclo de vida diferentes:</p>
            </div>

            <table>
                <tr>
                    <th>Tipo</th>
                    <th>Localização</th>
                    <th>Inicialização</th>
                    <th>Escopo</th>
                    <th>Ciclo de Vida</th>
                </tr>
                <tr>
                    <td><strong>Instance Variables</strong></td>
                    <td>Dentro da classe, fora de métodos</td>
                    <td>Automática (valores padrão)</td>
                    <td>Toda a classe</td>
                    <td>Enquanto objeto existir</td>
                </tr>
                <tr>
                    <td><strong>Static Variables</strong></td>
                    <td>Dentro da classe com 'static'</td>
                    <td>Automática (valores padrão)</td>
                    <td>Toda a classe</td>
                    <td>Enquanto programa executar</td>
                </tr>
                <tr>
                    <td><strong>Local Variables</strong></td>
                    <td>Dentro de métodos/blocos</td>
                    <td>Manual (obrigatória)</td>
                    <td>Método/bloco atual</td>
                    <td>Enquanto método/bloco executar</td>
                </tr>
                <tr>
                    <td><strong>Parameters</strong></td>
                    <td>Parâmetros de métodos</td>
                    <td>Automática (valor passado)</td>
                    <td>Método atual</td>
                    <td>Enquanto método executar</td>
                </tr>
            </table>

            <div class="example-box">
                <h4>Exemplo Completo de Tipos de Variáveis</h4>
                <div class="code-block">
public class VariableExample {
    // ✅ Instance Variable - uma por objeto
    private String instanceVar = "Instance";
    
    // ✅ Static Variable - compartilhada por todos os objetos
    private static int classVar = 100;
    
    // ✅ Static final - constante da classe
    public static final String CONSTANT = "CONSTANT_VALUE";
    
    public void demonstrateVariables(String parameter) { // ✅ Parameter
        // ✅ Local Variable - deve ser inicializada
        int localVar = 10;
        
        // ✅ Final local variable - pode ser inicializada depois
        final String finalVar;
        if (parameter != null) {
            finalVar = parameter.toUpperCase();
        } else {
            finalVar = "DEFAULT";
        }
        
        // ✅ Variável em bloco
        {
            int blockVar = 20; // só existe neste bloco
            System.out.println("Block var: " + blockVar);
        }
        // System.out.println(blockVar); // ❌ ERRO - fora de escopo
        
        // ✅ Variável em loop
        for (int i = 0; i &lt; 5; i++) { // 'i' só existe no loop
            int loopVar = i * 2;
            System.out.println("Loop var: " + loopVar);
        }
        // System.out.println(i); // ❌ ERRO - fora de escopo
    }
    
    // Método para demonstrar acesso às variáveis
    public void showVariableAccess() {
        System.out.println("Instance: " + instanceVar);     // ✅ OK
        System.out.println("Static: " + classVar);          // ✅ OK
        System.out.println("Constant: " + CONSTANT);        // ✅ OK
        // System.out.println(localVar);                    // ❌ ERRO - fora de escopo
    }
    
    public static void staticMethod() {
        System.out.println("Static: " + classVar);          // ✅ OK
        System.out.println("Constant: " + CONSTANT);        // ✅ OK
        // System.out.println(instanceVar);                 // ❌ ERRO - não pode acessar instance de static
    }
}
                </div>
            </div>

            <h3>🔢 TIPOS PRIMITIVOS - DETALHAMENTO COMPLETO</h3>

            <div class="difference-box">
                <h4>⚡ PERFORMANCE: Primitivos vs Objetos</h4>
                <div class="code-block">
// Teste de performance - Primitivos vs Wrapper Classes
public class PerformanceTest {
    public static void main(String[] args) {
        int iterations = 100_000_000;
        
        // ✅ Primitivos - RÁPIDO
        long start = System.currentTimeMillis();
        int sum1 = 0;
        for (int i = 0; i &lt; iterations; i++) {
            sum1 += i;
        }
        long primitiveTime = System.currentTimeMillis() - start;
        
        // ❌ Wrapper Classes - LENTO (autoboxing)
        start = System.currentTimeMillis();
        Integer sum2 = 0;
        for (int i = 0; i &lt; iterations; i++) {
            sum2 += i; // cria novo Integer a cada iteração!
        }
        long wrapperTime = System.currentTimeMillis() - start;
        
        System.out.println("Primitivo: " + primitiveTime + "ms");
        System.out.println("Wrapper: " + wrapperTime + "ms");
        System.out.println("Wrapper é " + (wrapperTime/primitiveTime) + "x mais lento!");
    }
}

// Resultado típico:
// Primitivo: 45ms
// Wrapper: 2847ms  
// Wrapper é 63x mais lento!
                </div>
            </div>

            <div class="example-box">
                <h4>Tipos Inteiros - Limites e Comportamentos</h4>
                <div class="code-block">
public class IntegerTypes {
    public static void main(String[] args) {
        // byte: 8 bits, -128 a 127
        byte minByte = -128;
        byte maxByte = 127;
        byte overflow = (byte) 128; // -128 (overflow circular)
        
        // short: 16 bits, -32,768 a 32,767
        short minShort = -32_768;
        short maxShort = 32_767;
        
        // int: 32 bits, -2^31 a 2^31-1 (padrão para literais)
        int minInt = Integer.MIN_VALUE;  // -2,147,483,648
        int maxInt = Integer.MAX_VALUE;  //  2,147,483,647
        
        // Diferentes representações de literais inteiros
        int decimal = 42;
        int binary = 0b101010;    // 42 em binário
        int octal = 0o52;         // 42 em octal (Java 7+)
        int hex = 0x2A;           // 42 em hexadecimal
        int withUnderscore = 1_000_000; // 1 milhão (Java 7+)
        
        // long: 64 bits, -2^63 a 2^63-1
        long minLong = Long.MIN_VALUE;
        long maxLong = Long.MAX_VALUE;
        long bigNumber = 9_223_372_036_854_775_807L; // sufixo L obrigatório
        
        // ⚠️ Overflow silencioso - cuidado!
        int almostMax = Integer.MAX_VALUE;
        int overflowed = almostMax + 1; // -2,147,483,648 (não gera erro!)
        
        // ✅ Detecção de overflow (Java 8+)
        try {
            int safe = Math.addExact(almostMax, 1);
        } catch (ArithmeticException e) {
            System.out.println("Overflow detectado: " + e.getMessage());
        }
        
        // ✅ Operações seguras
        int safeAdd = Math.addExact(100, 200);
        int safeMult = Math.multiplyExact(1000, 2000);
        long safeLongMult = Math.multiplyExact(1000L, 2000L);
    }
}
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ CUIDADO COM PONTO FLUTUANTE!</strong><br>
                Números de ponto flutuante não são exatos. Para cálculos financeiros, use BigDecimal.
            </div>

            <div class="example-box">
                <h4>Ponto Flutuante - Precisão e Armadilhas</h4>
                <div class="code-block">
public class FloatingPointIssues {
    public static void main(String[] args) {
        // ❌ Problema clássico de precisão
        double result1 = 0.1 + 0.2;
        System.out.println(result1);              // 0.30000000000000004
        System.out.println(result1 == 0.3);      // false !!!
        
        // ❌ Comparação incorreta
        double a = 1.0;
        double b = 0.9;
        double c = a - b;
        System.out.println(c);                    // 0.09999999999999998
        System.out.println(c == 0.1);            // false !!!
        
        // ✅ Comparação correta com epsilon
        double epsilon = 1e-10;
        boolean isEqual = Math.abs(c - 0.1) &lt; epsilon;
        System.out.println("Igual com epsilon: " + isEqual); // true
        
        // ✅ Para cálculos monetários - use BigDecimal
        BigDecimal bd1 = new BigDecimal("0.1");
        BigDecimal bd2 = new BigDecimal("0.2");
        BigDecimal bd3 = bd1.add(bd2);
        System.out.println("BigDecimal: " + bd3); // 0.3 (exato!)
        
        // Valores especiais
        double positiveInfinity = Double.POSITIVE_INFINITY;
        double negativeInfinity = Double.NEGATIVE_INFINITY;
        double notANumber = Double.NaN;
        
        // Verificações
        System.out.println("É infinito: " + Double.isInfinite(positiveInfinity));
        System.out.println("É NaN: " + Double.isNaN(notANumber));
        System.out.println("É finito: " + Double.isFinite(123.45));
        
        // ⚠️ NaN é especial - não é igual a nada, nem a si mesmo!
        System.out.println("NaN == NaN: " + (Double.NaN == Double.NaN)); // false !!!
    }
}
                </div>
            </div>
        </div>

        <div class="page-break" id="classes">
            <h2>PÁGINA 5-6: CLASSES, OBJETOS E MÉTODOS</h2>

            <div class="concept-box">
                <h3>🎯 ANATOMIA DE UMA CLASSE JAVA</h3>
                <p>Uma classe Java é um template para criar objetos. Ela define:</p>
                <ul>
                    <li><strong>Estado:</strong> Atributos (variáveis de instância)</li>
                    <li><strong>Comportamento:</strong> Métodos</li>
                    <li><strong>Identidade:</strong> Cada objeto tem uma identidade única</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>Classe Completa - Exemplo Detalhado</h4>
                <div class="code-block">
/**
 * Classe que representa uma Conta Bancária
 * Demonstra todos os conceitos fundamentais de OOP
 */
public class ContaBancaria {
    // ========== ATRIBUTOS (ESTADO) ==========

    // Atributos de instância (cada objeto tem sua cópia)
    private String numero;           // Encapsulado (private)
    private String titular;
    private double saldo;
    private LocalDateTime dataAbertura;

    // Atributo de classe (compartilhado por todos os objetos)
    private static int contadorContas = 0;

    // Constante da classe
    public static final double TAXA_JUROS = 0.05;

    // ========== CONSTRUTORES ==========

    // Construtor padrão
    public ContaBancaria() {
        this("000000", "Titular Padrão", 0.0);
    }

    // Construtor com parâmetros
    public ContaBancaria(String numero, String titular, double saldoInicial) {
        // Validações
        if (numero == null || numero.trim().isEmpty()) {
            throw new IllegalArgumentException("Número da conta não pode ser vazio");
        }
        if (titular == null || titular.trim().isEmpty()) {
            throw new IllegalArgumentException("Titular não pode ser vazio");
        }
        if (saldoInicial &lt; 0) {
            throw new IllegalArgumentException("Saldo inicial não pode ser negativo");
        }

        // Inicialização
        this.numero = numero;
        this.titular = titular;
        this.saldo = saldoInicial;
        this.dataAbertura = LocalDateTime.now();

        // Incrementa contador de contas
        contadorContas++;

        System.out.println("Conta criada: " + numero + " para " + titular);
    }

    // ========== MÉTODOS (COMPORTAMENTO) ==========

    // Método para depositar
    public void depositar(double valor) {
        validarValor(valor);
        saldo += valor;
        System.out.println("Depósito de R$ " + valor + " realizado. Saldo atual: R$ " + saldo);
    }

    // Método para sacar
    public boolean sacar(double valor) {
        validarValor(valor);

        if (valor &gt; saldo) {
            System.out.println("Saldo insuficiente. Saldo atual: R$ " + saldo);
            return false;
        }

        saldo -= valor;
        System.out.println("Saque de R$ " + valor + " realizado. Saldo atual: R$ " + saldo);
        return true;
    }

    // Método para transferir
    public boolean transferir(ContaBancaria contaDestino, double valor) {
        if (this.sacar(valor)) {
            contaDestino.depositar(valor);
            System.out.println("Transferência de R$ " + valor +
                             " realizada para conta " + contaDestino.getNumero());
            return true;
        }
        return false;
    }

    // Método privado para validação (auxiliar)
    private void validarValor(double valor) {
        if (valor &lt;= 0) {
            throw new IllegalArgumentException("Valor deve ser positivo");
        }
    }

    // ========== GETTERS E SETTERS ==========

    public String getNumero() {
        return numero;
    }

    public String getTitular() {
        return titular;
    }

    public void setTitular(String titular) {
        if (titular == null || titular.trim().isEmpty()) {
            throw new IllegalArgumentException("Titular não pode ser vazio");
        }
        this.titular = titular;
    }

    public double getSaldo() {
        return saldo;
    }

    // Não há setSaldo público - saldo só muda através de operações

    public LocalDateTime getDataAbertura() {
        return dataAbertura;
    }

    // ========== MÉTODOS ESTÁTICOS ==========

    public static int getTotalContas() {
        return contadorContas;
    }

    public static double calcularJuros(double valor) {
        return valor * TAXA_JUROS;
    }

    // ========== MÉTODOS SOBRESCRITOS ==========

    @Override
    public String toString() {
        return String.format("ContaBancaria{numero='%s', titular='%s', saldo=%.2f, dataAbertura=%s}",
                           numero, titular, saldo, dataAbertura.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        ContaBancaria that = (ContaBancaria) obj;
        return Objects.equals(numero, that.numero);
    }

    @Override
    public int hashCode() {
        return Objects.hash(numero);
    }

    // ========== FINALIZADOR (evitar usar) ==========

    @Override
    protected void finalize() throws Throwable {
        // ❌ Evite usar finalize() - use try-with-resources
        System.out.println("Conta " + numero + " sendo coletada pelo GC");
        super.finalize();
    }
}
                </div>
            </div>

            <div class="example-box">
                <h4>Uso da Classe - Demonstração Completa</h4>
                <div class="code-block">
public class TesteBanco {
    public static void main(String[] args) {
        // Criando objetos
        ContaBancaria conta1 = new ContaBancaria("12345", "João Silva", 1000.0);
        ContaBancaria conta2 = new ContaBancaria("67890", "Maria Santos", 500.0);

        // Operações
        conta1.depositar(200.0);
        conta1.sacar(150.0);
        conta1.transferir(conta2, 300.0);

        // Informações
        System.out.println(conta1);
        System.out.println(conta2);
        System.out.println("Total de contas: " + ContaBancaria.getTotalContas());

        // Comparação de objetos
        ContaBancaria conta3 = new ContaBancaria("12345", "Outro Titular", 0.0);
        System.out.println("conta1.equals(conta3): " + conta1.equals(conta3)); // true (mesmo número)

        // Método estático
        double juros = ContaBancaria.calcularJuros(1000.0);
        System.out.println("Juros de R$ 1000: R$ " + juros);
    }
}
                </div>
            </div>

            <h3>🔄 CICLO DE VIDA DE OBJETOS</h3>

            <div class="concept-box">
                <h4>Fases do Ciclo de Vida</h4>
                <ol>
                    <li><strong>Criação:</strong> new + construtor</li>
                    <li><strong>Uso:</strong> Chamadas de métodos</li>
                    <li><strong>Garbage Collection:</strong> Limpeza automática</li>
                </ol>
            </div>

            <div class="example-box">
                <h4>Demonstração do Ciclo de Vida</h4>
                <div class="code-block">
public class CicloVidaObjeto {
    private String nome;
    private static int contador = 0;

    // Construtor
    public CicloVidaObjeto(String nome) {
        this.nome = nome;
        contador++;
        System.out.println("✅ Objeto criado: " + nome + " (Total: " + contador + ")");
    }

    // Método de uso
    public void trabalhar() {
        System.out.println("🔄 " + nome + " está trabalhando...");
    }

    // Finalizador (demonstração - não use em produção)
    @Override
    protected void finalize() throws Throwable {
        contador--;
        System.out.println("🗑️ Objeto coletado: " + nome + " (Restam: " + contador + ")");
        super.finalize();
    }

    public static void main(String[] args) {
        // 1. CRIAÇÃO
        CicloVidaObjeto obj1 = new CicloVidaObjeto("Objeto1");
        CicloVidaObjeto obj2 = new CicloVidaObjeto("Objeto2");

        // 2. USO
        obj1.trabalhar();
        obj2.trabalhar();

        // 3. REMOÇÃO DE REFERÊNCIAS
        obj1 = null; // obj1 elegível para GC

        // Criação de muitos objetos para forçar GC
        for (int i = 0; i &lt; 1000; i++) {
            new CicloVidaObjeto("Temp" + i);
        }

        // Sugestão para GC (não garantia)
        System.gc();

        // Pausa para ver GC em ação
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        obj2.trabalhar(); // obj2 ainda existe
    }
}
                </div>
            </div>

            <h3>⚙️ MÉTODOS - DETALHAMENTO AVANÇADO</h3>

            <div class="example-box">
                <h4>Sobrecarga de Métodos (Overloading)</h4>
                <div class="code-block">
public class CalculadoraAvancada {

    // Sobrecarga por número de parâmetros
    public int somar(int a, int b) {
        System.out.println("Somando 2 inteiros");
        return a + b;
    }

    public int somar(int a, int b, int c) {
        System.out.println("Somando 3 inteiros");
        return a + b + c;
    }

    // Sobrecarga por tipo de parâmetros
    public double somar(double a, double b) {
        System.out.println("Somando 2 doubles");
        return a + b;
    }

    public String somar(String a, String b) {
        System.out.println("Concatenando strings");
        return a + b;
    }

    // Sobrecarga com varargs
    public int somar(int... numeros) {
        System.out.println("Somando " + numeros.length + " números");
        int soma = 0;
        for (int num : numeros) {
            soma += num;
        }
        return soma;
    }

    // ❌ ERRO - não pode sobrecarregar apenas por tipo de retorno
    // public double somar(int a, int b) { return a + b; }

    public static void main(String[] args) {
        CalculadoraAvancada calc = new CalculadoraAvancada();

        // Compilador escolhe método baseado nos argumentos
        calc.somar(1, 2);           // int, int
        calc.somar(1, 2, 3);        // int, int, int
        calc.somar(1.5, 2.5);       // double, double
        calc.somar("Hello", "World"); // String, String
        calc.somar(1, 2, 3, 4, 5);  // varargs

        // Promoção automática de tipos
        calc.somar(1, 2.0);         // int promovido para double
    }
}
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ CUIDADO COM VARARGS!</strong><br>
                Varargs deve ser o último parâmetro e só pode haver um por método.
            </div>

            <div class="example-box">
                <h4>Passagem de Parâmetros - Por Valor vs Por Referência</h4>
                <div class="code-block">
public class PassagemParametros {

    // Primitivos são passados por VALOR (cópia)
    public static void modificarPrimitivo(int x) {
        x = 100; // modifica apenas a cópia local
        System.out.println("Dentro do método: " + x); // 100
    }

    // Objetos são passados por REFERÊNCIA (cópia da referência)
    public static void modificarObjeto(StringBuilder sb) {
        sb.append(" Modificado"); // modifica o objeto original
        System.out.println("Dentro do método: " + sb);
    }

    // Reatribuição não afeta a referência original
    public static void reatribuirReferencia(StringBuilder sb) {
        sb = new StringBuilder("Nova String"); // cria novo objeto
        sb.append(" Local");
        System.out.println("Dentro do método: " + sb); // "Nova String Local"
    }

    // Arrays são objetos - passados por referência
    public static void modificarArray(int[] arr) {
        arr[0] = 999; // modifica o array original
    }

    public static void main(String[] args) {
        // Teste com primitivo
        int numero = 10;
        modificarPrimitivo(numero);
        System.out.println("Após método: " + numero); // 10 (não mudou)

        // Teste com objeto
        StringBuilder sb = new StringBuilder("Original");
        modificarObjeto(sb);
        System.out.println("Após método: " + sb); // "Original Modificado"

        // Teste com reatribuição
        StringBuilder sb2 = new StringBuilder("Original");
        reatribuirReferencia(sb2);
        System.out.println("Após reatribuição: " + sb2); // "Original" (não mudou)

        // Teste com array
        int[] array = {1, 2, 3};
        modificarArray(array);
        System.out.println("Array após método: " + Arrays.toString(array)); // [999, 2, 3]
    }
}
                </div>
            </div>
        </div>

        <div class="page-break" id="modificadores">
            <h2>PÁGINA 7: MODIFICADORES DE ACESSO E PACOTES</h2>

            <div class="concept-box">
                <h3>🔒 MODIFICADORES DE ACESSO</h3>
                <p>Java possui 4 níveis de acesso que controlam a visibilidade de classes, métodos e atributos:</p>
            </div>

            <table>
                <tr>
                    <th>Modificador</th>
                    <th>Mesma Classe</th>
                    <th>Mesmo Pacote</th>
                    <th>Subclasse</th>
                    <th>Qualquer Lugar</th>
                    <th>Uso Recomendado</th>
                </tr>
                <tr>
                    <td><strong>private</strong></td>
                    <td>✅</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>Atributos, métodos auxiliares</td>
                </tr>
                <tr>
                    <td><strong>package-private</strong></td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>❌</td>
                    <td>❌</td>
                    <td>Classes internas do pacote</td>
                </tr>
                <tr>
                    <td><strong>protected</strong></td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>❌</td>
                    <td>Herança, métodos para subclasses</td>
                </tr>
                <tr>
                    <td><strong>public</strong></td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>✅</td>
                    <td>APIs públicas, interfaces</td>
                </tr>
            </table>

            <div class="example-box">
                <h4>Demonstração Completa de Modificadores</h4>
                <div class="code-block">
// Arquivo: com/empresa/modelo/Pessoa.java
package com.empresa.modelo;

public class Pessoa {
    // ✅ private - só esta classe acessa
    private String cpf;
    private double salario;

    // ✅ package-private - classes do mesmo pacote acessam
    String codigoInterno;

    // ✅ protected - subclasses e mesmo pacote acessam
    protected String nome;
    protected int idade;

    // ✅ public - todos acessam
    public String email;

    // Construtor público
    public Pessoa(String nome, int idade, String email) {
        this.nome = nome;
        this.idade = idade;
        this.email = email;
        this.codigoInterno = gerarCodigoInterno(); // método privado
    }

    // ✅ Método privado - só esta classe usa
    private String gerarCodigoInterno() {
        return "PES" + System.currentTimeMillis();
    }

    // ✅ Método package-private - classes do pacote usam
    void atualizarCodigoInterno() {
        this.codigoInterno = gerarCodigoInterno();
    }

    // ✅ Método protected - subclasses podem sobrescrever
    protected void validarIdade() {
        if (idade &lt; 0 || idade &gt; 150) {
            throw new IllegalArgumentException("Idade inválida: " + idade);
        }
    }

    // ✅ Getters/Setters públicos para atributos privados
    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        // Validação antes de definir
        if (cpf == null || !cpf.matches("\\d{11}")) {
            throw new IllegalArgumentException("CPF inválido");
        }
        this.cpf = cpf;
    }

    public double getSalario() {
        return salario;
    }

    public void setSalario(double salario) {
        if (salario &lt; 0) {
            throw new IllegalArgumentException("Salário não pode ser negativo");
        }
        this.salario = salario;
    }

    // ✅ Método público - interface da classe
    public String getInformacoes() {
        return String.format("Nome: %s, Idade: %d, Email: %s", nome, idade, email);
    }
}

// Arquivo: com/empresa/modelo/Funcionario.java (mesmo pacote)
package com.empresa.modelo;

public class Funcionario extends Pessoa {
    private String cargo;

    public Funcionario(String nome, int idade, String email, String cargo) {
        super(nome, idade, email);
        this.cargo = cargo;
    }

    @Override
    protected void validarIdade() {
        // ✅ Pode acessar método protected da superclasse
        super.validarIdade();

        // Validação adicional para funcionários
        if (idade &lt; 16) {
            throw new IllegalArgumentException("Funcionário deve ter pelo menos 16 anos");
        }
    }

    public void promover() {
        // ✅ Pode acessar atributos protected
        System.out.println("Promovendo " + nome + " de " + idade + " anos");

        // ✅ Pode acessar método package-private (mesmo pacote)
        atualizarCodigoInterno();

        // ❌ Não pode acessar atributos private da superclasse
        // System.out.println(cpf); // ERRO de compilação
    }
}

// Arquivo: com/empresa/teste/TestePessoa.java (pacote diferente)
package com.empresa.teste;

import com.empresa.modelo.Pessoa;
import com.empresa.modelo.Funcionario;

public class TestePessoa {
    public static void main(String[] args) {
        Pessoa pessoa = new Pessoa("João", 30, "<EMAIL>");

        // ✅ Pode acessar membros public
        System.out.println(pessoa.email);
        System.out.println(pessoa.getInformacoes());

        // ❌ Não pode acessar membros protected (pacote diferente)
        // System.out.println(pessoa.nome); // ERRO

        // ❌ Não pode acessar membros package-private
        // pessoa.atualizarCodigoInterno(); // ERRO

        // ❌ Não pode acessar membros private
        // System.out.println(pessoa.cpf); // ERRO
    }
}
                </div>
            </div>

            <h3>📦 ORGANIZAÇÃO EM PACOTES</h3>

            <div class="concept-box">
                <h4>Convenções de Nomenclatura de Pacotes</h4>
                <ul>
                    <li><strong>Domínio reverso:</strong> com.empresa.projeto</li>
                    <li><strong>Tudo minúsculo:</strong> sem CamelCase</li>
                    <li><strong>Sem caracteres especiais:</strong> apenas letras, números e pontos</li>
                    <li><strong>Hierárquico:</strong> do geral para o específico</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>Estrutura de Pacotes Recomendada</h4>
                <div class="code-block">
// Estrutura típica de projeto Java
src/
└── main/
    └── java/
        └── com/
            └── empresa/
                └── projeto/
                    ├── modelo/          // Entidades, DTOs
                    │   ├── Usuario.java
                    │   ├── Produto.java
                    │   └── dto/
                    │       ├── UsuarioDTO.java
                    │       └── ProdutoDTO.java
                    ├── repositorio/     // Acesso a dados
                    │   ├── UsuarioRepository.java
                    │   └── ProdutoRepository.java
                    ├── servico/         // Lógica de negócio
                    │   ├── UsuarioService.java
                    │   └── ProdutoService.java
                    ├── controlador/     // Controllers REST
                    │   ├── UsuarioController.java
                    │   └── ProdutoController.java
                    ├── config/          // Configurações
                    │   ├── DatabaseConfig.java
                    │   └── SecurityConfig.java
                    ├── util/            // Utilitários
                    │   ├── DateUtils.java
                    │   └── StringUtils.java
                    └── exception/       // Exceções customizadas
                        ├── UsuarioNaoEncontradoException.java
                        └── ValidacaoException.java
                </div>
            </div>

            <div class="tip">
                <strong>💡 DICA IMPORTANTE:</strong><br>
                Use package-private para classes que são detalhes de implementação e não devem ser usadas fora do pacote.
            </div>
        </div>
    </div>
</body>
</html>
