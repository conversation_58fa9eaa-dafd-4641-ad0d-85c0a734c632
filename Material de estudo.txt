Fundamentos de Java
    0. Diferenças entre cada atualização desde a versão 6 até a atual
    1. Sintaxe básica (variáveis, operadores, controle de fluxo)
    2. Tipos de dados primitivos
    3. Classes e objetos
    4. Métodos e construtores
    5. Modificadores de acesso (public, private, protected)
    6. Pacotes e import
Programação Orientada a Objetos (POO)
    1. Encapsulamento
    2. Herança
    3. Polimorfismo
    4. Abstração
    5. Classes abstratas e interfaces
Coleções e Estruturas de Dados
    1. Arrays
    2. ArrayList, LinkedList
    3. HashMap, HashSet, TreeMap, TreeSet
    4. <PERSON>ue, Deque, Stack
    5. Iteradores e Streams
Tratamento de Erros
    1. Try-catch-finally
    2. Lançamento de exceções (throw / throws)
    3. Exceções personalizadas
Boas Práticas
    1. Princípios SOLID
    2. Clean Code (legibilidade, responsabilidade única, nomes significativos)
    3. Refatoração
    4. <PERSON><PERSON>, KISS, YAGNI
Arquitetura e Design
    1. Design Patterns (Factory, Singleton, Strategy, Observer, etc.)
    2. Arquitetura em camadas (Controller, Service, Repository)
    3. DTOs e mapeamento de dados
Java Avançado
    1. Generics
    2. Lambda Expressions
    3. Streams API
    4. Anotações (Annotations)
    5. Reflection
    6. Enumerações
Integração e Comunicação
    1. APIs REST usando Spring Boot
    2. Serialização JSON (Jackson, Gson)
    3. WebSockets
    4. Broker de mensagens (RabbitMQ, Kafka)
    5. Consumo de APIs externas (RestTemplate, WebClient)
Concorrência e Multithreading
    1. Threads e Runnable
    2. ExecutorService
    3. synchronized, volatile
    4. CompletableFuture
Testes
    1. JUnit 5
    2. Mockito
    3. Testes de unidade
    4. Testes de integração
    5. Testes com Spring Boot
Ferramentas e Ecossistema
    1. Maven / Gradle
    2. Git e GitFlow
    3. Docker (para containerizar aplicações)
    4. CI/CD (GitHub Actions, GitLab CI, Jenkins)
    5. Monitoramento (Actuator, Prometheus, etc.)



